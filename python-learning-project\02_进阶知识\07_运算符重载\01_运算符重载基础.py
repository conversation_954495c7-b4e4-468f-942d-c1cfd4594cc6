#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运算符重载 (Operator Overloading) - Python运算符重载机制
Operator Overloading - Python Operator Overloading Mechanism

运算符重载允许自定义类支持内置运算符，使对象的使用更加直观和自然。
Operator overloading allows custom classes to support built-in operators, making object usage more intuitive and natural.
"""

from typing import Union, Any
import math

def basic_arithmetic_operators():
    """基本算术运算符 / Basic Arithmetic Operators"""
    print("=== 基本算术运算符 Basic Arithmetic Operators ===")
    
    class Vector2D:
        """二维向量类 - 演示算术运算符重载"""
        
        def __init__(self, x: float, y: float):
            self.x = x
            self.y = y
        
        def __add__(self, other):
            """加法运算符 +"""
            if isinstance(other, Vector2D):
                return Vector2D(self.x + other.x, self.y + other.y)
            elif isinstance(other, (int, float)):
                return Vector2D(self.x + other, self.y + other)
            return NotImplemented
        
        def __radd__(self, other):
            """反向加法 (当左操作数不支持加法时)"""
            return self.__add__(other)
        
        def __sub__(self, other):
            """减法运算符 -"""
            if isinstance(other, Vector2D):
                return Vector2D(self.x - other.x, self.y - other.y)
            elif isinstance(other, (int, float)):
                return Vector2D(self.x - other, self.y - other)
            return NotImplemented
        
        def __mul__(self, other):
            """乘法运算符 *"""
            if isinstance(other, Vector2D):
                # 点积 / Dot product
                return self.x * other.x + self.y * other.y
            elif isinstance(other, (int, float)):
                # 标量乘法 / Scalar multiplication
                return Vector2D(self.x * other, self.y * other)
            return NotImplemented
        
        def __rmul__(self, other):
            """反向乘法"""
            return self.__mul__(other)
        
        def __truediv__(self, other):
            """除法运算符 /"""
            if isinstance(other, (int, float)):
                if other == 0:
                    raise ZeroDivisionError("不能除以零")
                return Vector2D(self.x / other, self.y / other)
            return NotImplemented
        
        def __neg__(self):
            """负号运算符 -"""
            return Vector2D(-self.x, -self.y)
        
        def __abs__(self):
            """绝对值运算符 abs()"""
            return math.sqrt(self.x ** 2 + self.y ** 2)
        
        def __repr__(self):
            return f"Vector2D({self.x}, {self.y})"
    
    print("1. 向量算术运算:")
    v1 = Vector2D(3, 4)
    v2 = Vector2D(1, 2)
    
    print(f"v1 = {v1}")
    print(f"v2 = {v2}")
    
    print(f"v1 + v2 = {v1 + v2}")
    print(f"v1 - v2 = {v1 - v2}")
    print(f"v1 * v2 = {v1 * v2}")  # 点积
    print(f"v1 * 2 = {v1 * 2}")   # 标量乘法
    print(f"3 * v1 = {3 * v1}")   # 反向乘法
    print(f"v1 / 2 = {v1 / 2}")
    print(f"-v1 = {-v1}")
    print(f"abs(v1) = {abs(v1)}")
    
    print("\n2. 与标量的运算:")
    print(f"v1 + 5 = {v1 + 5}")
    print(f"v1 - 3 = {v1 - 3}")

def comparison_operators():
    """比较运算符 / Comparison Operators"""
    print("\n=== 比较运算符 Comparison Operators ===")
    
    class Student:
        """学生类 - 演示比较运算符重载"""
        
        def __init__(self, name: str, grade: float):
            self.name = name
            self.grade = grade
        
        def __eq__(self, other):
            """等于运算符 =="""
            if isinstance(other, Student):
                return self.grade == other.grade
            elif isinstance(other, (int, float)):
                return self.grade == other
            return NotImplemented
        
        def __lt__(self, other):
            """小于运算符 <"""
            if isinstance(other, Student):
                return self.grade < other.grade
            elif isinstance(other, (int, float)):
                return self.grade < other
            return NotImplemented
        
        def __le__(self, other):
            """小于等于运算符 <="""
            if isinstance(other, Student):
                return self.grade <= other.grade
            elif isinstance(other, (int, float)):
                return self.grade <= other
            return NotImplemented
        
        def __gt__(self, other):
            """大于运算符 >"""
            if isinstance(other, Student):
                return self.grade > other.grade
            elif isinstance(other, (int, float)):
                return self.grade > other
            return NotImplemented
        
        def __ge__(self, other):
            """大于等于运算符 >="""
            if isinstance(other, Student):
                return self.grade >= other.grade
            elif isinstance(other, (int, float)):
                return self.grade >= other
            return NotImplemented
        
        def __ne__(self, other):
            """不等于运算符 !="""
            result = self.__eq__(other)
            if result is NotImplemented:
                return result
            return not result
        
        def __hash__(self):
            """哈希函数 - 使对象可以用作字典键或集合元素"""
            return hash((self.name, self.grade))
        
        def __repr__(self):
            return f"Student('{self.name}', {self.grade})"
    
    print("1. 学生成绩比较:")
    alice = Student("Alice", 85)
    bob = Student("Bob", 92)
    charlie = Student("Charlie", 85)
    
    print(f"alice = {alice}")
    print(f"bob = {bob}")
    print(f"charlie = {charlie}")
    
    print(f"\n比较结果:")
    print(f"alice == charlie: {alice == charlie}")
    print(f"alice == bob: {alice == bob}")
    print(f"alice < bob: {alice < bob}")
    print(f"bob > alice: {bob > alice}")
    print(f"alice <= charlie: {alice <= charlie}")
    print(f"bob >= alice: {bob >= alice}")
    print(f"alice != bob: {alice != bob}")
    
    print(f"\n与数值比较:")
    print(f"alice == 85: {alice == 85}")
    print(f"alice > 80: {alice > 80}")
    print(f"alice < 90: {alice < 90}")
    
    print(f"\n排序:")
    students = [bob, alice, charlie]
    print(f"排序前: {students}")
    students.sort()
    print(f"排序后: {students}")

def container_operators():
    """容器运算符 / Container Operators"""
    print("\n=== 容器运算符 Container Operators ===")
    
    class Matrix:
        """矩阵类 - 演示容器运算符重载"""
        
        def __init__(self, data):
            self.data = data
            self.rows = len(data)
            self.cols = len(data[0]) if data else 0
        
        def __getitem__(self, key):
            """索引访问 []"""
            if isinstance(key, tuple):
                row, col = key
                return self.data[row][col]
            else:
                return self.data[key]
        
        def __setitem__(self, key, value):
            """索引赋值 []"""
            if isinstance(key, tuple):
                row, col = key
                self.data[row][col] = value
            else:
                self.data[key] = value
        
        def __len__(self):
            """长度函数 len()"""
            return self.rows
        
        def __contains__(self, item):
            """成员测试 in"""
            for row in self.data:
                if item in row:
                    return True
            return False
        
        def __iter__(self):
            """迭代器支持"""
            for row in self.data:
                for item in row:
                    yield item
        
        def __repr__(self):
            return f"Matrix({self.data})"
    
    print("1. 矩阵容器操作:")
    matrix = Matrix([[1, 2, 3], [4, 5, 6], [7, 8, 9]])
    
    print(f"矩阵: {matrix}")
    print(f"长度: {len(matrix)}")
    print(f"matrix[1]: {matrix[1]}")
    print(f"matrix[1, 2]: {matrix[1, 2]}")
    
    print(f"\n成员测试:")
    print(f"5 in matrix: {5 in matrix}")
    print(f"10 in matrix: {10 in matrix}")
    
    print(f"\n修改元素:")
    matrix[1, 2] = 99
    print(f"修改后 matrix[1, 2]: {matrix[1, 2]}")
    
    print(f"\n迭代矩阵:")
    print("所有元素:", list(matrix))

def string_representation():
    """字符串表示 / String Representation"""
    print("\n=== 字符串表示 String Representation ===")
    
    class Book:
        """书籍类 - 演示字符串表示方法"""
        
        def __init__(self, title: str, author: str, pages: int, price: float):
            self.title = title
            self.author = author
            self.pages = pages
            self.price = price
        
        def __str__(self):
            """用户友好的字符串表示 - str()"""
            return f"《{self.title}》 by {self.author}"
        
        def __repr__(self):
            """开发者友好的字符串表示 - repr()"""
            return f"Book('{self.title}', '{self.author}', {self.pages}, {self.price})"
        
        def __format__(self, format_spec):
            """格式化字符串支持 - format()"""
            if format_spec == 'short':
                return f"{self.title} - {self.author}"
            elif format_spec == 'price':
                return f"《{self.title}》: ¥{self.price:.2f}"
            elif format_spec == 'full':
                return f"《{self.title}》 by {self.author} ({self.pages} pages, ¥{self.price:.2f})"
            else:
                return str(self)
    
    print("1. 字符串表示方法:")
    book = Book("Python编程", "张三", 500, 89.9)
    
    print(f"str(book): {str(book)}")
    print(f"repr(book): {repr(book)}")
    print(f"直接打印: {book}")
    
    print(f"\n2. 格式化字符串:")
    print(f"短格式: {book:short}")
    print(f"价格格式: {book:price}")
    print(f"完整格式: {book:full}")
    
    print(f"\n3. 在容器中的表示:")
    books = [book, Book("Java编程", "李四", 600, 99.9)]
    print(f"列表中的书籍: {books}")

def callable_objects():
    """可调用对象 / Callable Objects"""
    print("\n=== 可调用对象 Callable Objects ===")
    
    class Multiplier:
        """乘法器类 - 演示可调用对象"""
        
        def __init__(self, factor):
            self.factor = factor
        
        def __call__(self, value):
            """使对象可调用"""
            return value * self.factor
        
        def __repr__(self):
            return f"Multiplier({self.factor})"
    
    class Counter:
        """计数器类 - 有状态的可调用对象"""
        
        def __init__(self, start=0):
            self.count = start
        
        def __call__(self, increment=1):
            """每次调用增加计数"""
            self.count += increment
            return self.count
        
        def reset(self):
            """重置计数器"""
            self.count = 0
        
        def __repr__(self):
            return f"Counter(count={self.count})"
    
    print("1. 乘法器对象:")
    double = Multiplier(2)
    triple = Multiplier(3)
    
    print(f"double = {double}")
    print(f"double(5) = {double(5)}")
    print(f"triple(4) = {triple(4)}")
    print(f"callable(double): {callable(double)}")
    
    print("\n2. 计数器对象:")
    counter = Counter()
    print(f"counter = {counter}")
    
    print("调用计数器:")
    for i in range(5):
        result = counter()
        print(f"  第{i+1}次调用: {result}")
    
    print(f"最终状态: {counter}")
    
    counter.reset()
    print(f"重置后: {counter}")

def context_manager_operators():
    """上下文管理器运算符 / Context Manager Operators"""
    print("\n=== 上下文管理器运算符 Context Manager Operators ===")
    
    class FileManager:
        """文件管理器 - 演示上下文管理器协议"""
        
        def __init__(self, filename, mode='r'):
            self.filename = filename
            self.mode = mode
            self.file = None
        
        def __enter__(self):
            """进入上下文"""
            print(f"打开文件: {self.filename}")
            self.file = open(self.filename, self.mode, encoding='utf-8')
            return self.file
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            """退出上下文"""
            if self.file:
                print(f"关闭文件: {self.filename}")
                self.file.close()
            
            if exc_type:
                print(f"处理异常: {exc_type.__name__}: {exc_val}")
                return False  # 不抑制异常
            
            return True
    
    class Timer:
        """计时器上下文管理器"""
        
        def __init__(self, name="操作"):
            self.name = name
            self.start_time = None
        
        def __enter__(self):
            import time
            print(f"开始 {self.name}")
            self.start_time = time.time()
            return self
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            import time
            end_time = time.time()
            duration = end_time - self.start_time
            print(f"{self.name} 完成，耗时: {duration:.4f} 秒")
            return False
    
    print("1. 文件管理器:")
    
    # 创建测试文件 / Create test file
    with open("test.txt", "w", encoding="utf-8") as f:
        f.write("Hello, World!\n这是测试文件。")
    
    # 使用自定义文件管理器 / Use custom file manager
    with FileManager("test.txt", "r") as f:
        content = f.read()
        print(f"文件内容: {content}")
    
    print("\n2. 计时器:")
    with Timer("数据处理"):
        # 模拟一些工作 / Simulate some work
        import time
        time.sleep(0.1)
        result = sum(range(1000000))
        print(f"计算结果: {result}")
    
    # 清理测试文件 / Clean up test file
    import os
    os.remove("test.txt")

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python运算符重载学习示例")
    print("Python Operator Overloading Learning Examples")
    print("=" * 60)
    
    basic_arithmetic_operators()
    comparison_operators()
    container_operators()
    string_representation()
    callable_objects()
    context_manager_operators()
    
    print("\n" + "=" * 60)
    print("运算符重载学习完成！Operator overloading learning completed!")
