#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
元组 (Tuple) - Python不可变序列
Tuples - Python Immutable Sequence

元组是有序、不可变的数据结构，类似于列表但不能修改。
Tuples are ordered, immutable data structures, similar to lists but cannot be modified.
"""

def basic_tuple_operations():
    """基本元组操作 / Basic Tuple Operations"""
    print("=== 基本元组操作 Basic Tuple Operations ===")
    
    # 创建元组 / Creating tuples
    empty_tuple = ()  # 空元组 / Empty tuple
    single_tuple = (42,)  # 单元素元组(注意逗号) / Single element tuple (note the comma)
    coordinates = (10, 20)  # 坐标点 / Coordinate point
    colors = ("红色", "绿色", "蓝色")  # 颜色元组 / Color tuple
    mixed = (1, "hello", 3.14, True)  # 混合类型 / Mixed types
    
    print(f"空元组: {empty_tuple}")
    print(f"单元素元组: {single_tuple}")
    print(f"坐标: {coordinates}")
    print(f"颜色: {colors}")
    print(f"混合元组: {mixed}")
    
    # 不使用括号创建元组 / Creating tuples without parentheses
    point = 5, 10  # 元组打包 / Tuple packing
    print(f"点坐标: {point}")
    
    # 访问元素 / Accessing elements
    print(f"第一个颜色: {colors[0]}")
    print(f"最后一个颜色: {colors[-1]}")
    
    # 元组长度 / Tuple length
    print(f"颜色数量: {len(colors)}")

def tuple_unpacking():
    """元组解包 / Tuple Unpacking"""
    print("\n=== 元组解包 Tuple Unpacking ===")
    
    # 基本解包 / Basic unpacking
    point = (3, 4)
    x, y = point  # 元组解包 / Tuple unpacking
    print(f"坐标点 {point} -> x={x}, y={y}")
    
    # 多个值的解包 / Multiple value unpacking
    person = ("张三", 25, "工程师")
    name, age, job = person
    print(f"姓名: {name}, 年龄: {age}, 职业: {job}")
    
    # 交换变量 / Swapping variables
    a, b = 10, 20
    print(f"交换前: a={a}, b={b}")
    a, b = b, a  # 使用元组交换 / Swap using tuple
    print(f"交换后: a={a}, b={b}")
    
    # 函数返回多个值 / Function returning multiple values
    def get_name_age():
        return "李四", 30
    
    name, age = get_name_age()
    print(f"函数返回: 姓名={name}, 年龄={age}")
    
    # 扩展解包 / Extended unpacking
    numbers = (1, 2, 3, 4, 5)
    first, *middle, last = numbers
    print(f"第一个: {first}, 中间: {middle}, 最后: {last}")

def tuple_methods_and_operations():
    """元组方法和操作 / Tuple Methods and Operations"""
    print("\n=== 元组方法和操作 Tuple Methods and Operations ===")
    
    numbers = (1, 2, 3, 2, 4, 2, 5)
    print(f"数字元组: {numbers}")
    
    # 计数 / Counting
    count_2 = numbers.count(2)
    print(f"数字2出现次数: {count_2}")
    
    # 查找索引 / Finding index
    index_3 = numbers.index(3)
    print(f"数字3的索引: {index_3}")
    
    # 元组切片 / Tuple slicing
    print(f"前3个元素: {numbers[:3]}")
    print(f"后3个元素: {numbers[-3:]}")
    print(f"每隔一个: {numbers[::2]}")
    
    # 元组连接 / Tuple concatenation
    more_numbers = (6, 7, 8)
    combined = numbers + more_numbers
    print(f"连接后: {combined}")
    
    # 元组重复 / Tuple repetition
    repeated = (1, 2) * 3
    print(f"重复元组: {repeated}")
    
    # 成员检查 / Membership testing
    print(f"3 在元组中: {3 in numbers}")
    print(f"10 在元组中: {10 in numbers}")

def nested_tuples():
    """嵌套元组 / Nested Tuples"""
    print("\n=== 嵌套元组 Nested Tuples ===")
    
    # 创建嵌套元组 / Creating nested tuples
    matrix = ((1, 2, 3), (4, 5, 6), (7, 8, 9))
    print(f"矩阵: {matrix}")
    
    # 访问嵌套元素 / Accessing nested elements
    print(f"第一行: {matrix[0]}")
    print(f"第二行第三列: {matrix[1][2]}")
    
    # 学生信息 / Student information
    students = (
        ("张三", 20, ("数学", 85)),
        ("李四", 21, ("物理", 92)),
        ("王五", 19, ("化学", 78))
    )
    
    print("\n学生信息:")
    for student in students:
        name, age, (subject, grade) = student
        print(f"姓名: {name}, 年龄: {age}, 科目: {subject}, 成绩: {grade}")

def tuple_vs_list():
    """元组与列表的比较 / Tuple vs List Comparison"""
    print("\n=== 元组与列表的比较 Tuple vs List Comparison ===")
    
    # 性能比较 / Performance comparison
    import time
    
    # 创建时间比较 / Creation time comparison
    start = time.time()
    for _ in range(100000):
        t = (1, 2, 3, 4, 5)
    tuple_time = time.time() - start
    
    start = time.time()
    for _ in range(100000):
        l = [1, 2, 3, 4, 5]
    list_time = time.time() - start
    
    print(f"创建10万个元组耗时: {tuple_time:.6f}秒")
    print(f"创建10万个列表耗时: {list_time:.6f}秒")
    
    # 内存使用比较 / Memory usage comparison
    import sys
    tuple_data = (1, 2, 3, 4, 5)
    list_data = [1, 2, 3, 4, 5]
    
    print(f"元组内存占用: {sys.getsizeof(tuple_data)} 字节")
    print(f"列表内存占用: {sys.getsizeof(list_data)} 字节")
    
    # 使用场景说明 / Use case explanation
    print("\n使用场景:")
    print("元组适用于: 坐标点、RGB颜色值、数据库记录、配置信息等不变数据")
    print("列表适用于: 购物清单、学生名单、动态数据集合等可变数据")

def practical_examples():
    """实际应用示例 / Practical Examples"""
    print("\n=== 实际应用示例 Practical Examples ===")
    
    # 1. 坐标系统 / Coordinate system
    print("1. 坐标系统示例:")
    points = [(0, 0), (1, 1), (2, 4), (3, 9)]  # 点的列表，每个点是元组
    
    for i, (x, y) in enumerate(points):
        print(f"点{i+1}: ({x}, {y})")
    
    # 计算距离 / Calculate distance
    def distance(p1, p2):
        x1, y1 = p1
        x2, y2 = p2
        return ((x2-x1)**2 + (y2-y1)**2)**0.5
    
    dist = distance(points[0], points[1])
    print(f"点1到点2的距离: {dist:.2f}")
    
    # 2. 数据库记录模拟 / Database record simulation
    print("\n2. 数据库记录示例:")
    employees = [
        ("E001", "张三", "开发部", 8000),
        ("E002", "李四", "测试部", 7000),
        ("E003", "王五", "产品部", 9000)
    ]
    
    print("员工信息:")
    for emp_id, name, dept, salary in employees:
        print(f"ID: {emp_id}, 姓名: {name}, 部门: {dept}, 薪资: {salary}")
    
    # 计算平均薪资 / Calculate average salary
    total_salary = sum(salary for _, _, _, salary in employees)
    avg_salary = total_salary / len(employees)
    print(f"平均薪资: {avg_salary:.2f}")
    
    # 3. 配置信息 / Configuration information
    print("\n3. 配置信息示例:")
    config = (
        ("database_host", "localhost"),
        ("database_port", 5432),
        ("debug_mode", True),
        ("max_connections", 100)
    )
    
    print("系统配置:")
    for key, value in config:
        print(f"{key}: {value}")

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python元组(Tuple)学习示例")
    print("Python Tuple Learning Examples")
    print("=" * 50)
    
    basic_tuple_operations()
    tuple_unpacking()
    tuple_methods_and_operations()
    nested_tuples()
    tuple_vs_list()
    practical_examples()
    
    print("\n" + "=" * 50)
    print("元组学习完成！Tuple learning completed!")
