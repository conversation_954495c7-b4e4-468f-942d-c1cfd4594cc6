#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对象和深浅复制 (Object and Deep/Shallow Copy) - Python对象复制机制
Object and Deep/Shallow Copy - Python Object Copying Mechanism

理解Python中对象的复制机制对于避免意外的数据修改和内存管理非常重要。
Understanding Python's object copying mechanism is crucial for avoiding unexpected data modifications and memory management.
"""

import copy
from typing import List, Dict, Any

def object_identity_and_equality():
    """对象身份和相等性 / Object Identity and Equality"""
    print("=== 对象身份和相等性 Object Identity and Equality ===")
    
    print("1. 对象身份 (is) vs 相等性 (==):")
    
    # 整数对象 / Integer objects
    a = 1000
    b = 1000
    c = a
    
    print(f"a = {a}, b = {b}, c = {c}")
    print(f"a == b: {a == b}")  # 值相等
    print(f"a is b: {a is b}")  # 身份不同 (大整数)
    print(f"a is c: {a is c}")  # 身份相同
    print(f"id(a): {id(a)}, id(b): {id(b)}, id(c): {id(c)}")
    
    print("\n2. 小整数缓存:")
    x = 5
    y = 5
    print(f"x = {x}, y = {y}")
    print(f"x is y: {x is y}")  # 小整数被缓存
    print(f"id(x): {id(x)}, id(y): {id(y)}")
    
    print("\n3. 字符串对象:")
    str1 = "hello"
    str2 = "hello"
    str3 = "hello world"
    str4 = "hello world"
    
    print(f"str1 is str2: {str1 is str2}")  # 字符串驻留
    print(f"str3 is str4: {str3 is str4}")  # 可能被驻留
    
    print("\n4. 列表对象:")
    list1 = [1, 2, 3]
    list2 = [1, 2, 3]
    list3 = list1
    
    print(f"list1 == list2: {list1 == list2}")  # 值相等
    print(f"list1 is list2: {list1 is list2}")  # 身份不同
    print(f"list1 is list3: {list1 is list3}")  # 身份相同
    
    # 修改list1会影响list3 / Modifying list1 affects list3
    list1.append(4)
    print(f"修改后 list1: {list1}")
    print(f"修改后 list3: {list3}")
    print(f"修改后 list2: {list2}")

def shallow_copy_examples():
    """浅复制示例 / Shallow Copy Examples"""
    print("\n=== 浅复制示例 Shallow Copy Examples ===")
    
    print("1. 使用切片进行浅复制:")
    original = [1, 2, [3, 4], 5]
    shallow_copy1 = original[:]  # 切片复制
    shallow_copy2 = list(original)  # list()构造函数
    shallow_copy3 = original.copy()  # copy()方法
    
    print(f"原始列表: {original}")
    print(f"浅复制1: {shallow_copy1}")
    print(f"original is shallow_copy1: {original is shallow_copy1}")
    print(f"original[2] is shallow_copy1[2]: {original[2] is shallow_copy1[2]}")
    
    print("\n2. 浅复制的特点:")
    # 修改顶层元素 / Modify top-level elements
    shallow_copy1.append(6)
    print(f"添加元素后:")
    print(f"原始列表: {original}")
    print(f"浅复制1: {shallow_copy1}")
    
    # 修改嵌套对象 / Modify nested objects
    original[2].append(7)
    print(f"修改嵌套列表后:")
    print(f"原始列表: {original}")
    print(f"浅复制1: {shallow_copy1}")  # 嵌套对象被共享!
    
    print("\n3. 使用copy.copy():")
    import copy
    
    class Person:
        def __init__(self, name, hobbies):
            self.name = name
            self.hobbies = hobbies
        
        def __repr__(self):
            return f"Person('{self.name}', {self.hobbies})"
    
    person1 = Person("张三", ["读书", "游泳"])
    person2 = copy.copy(person1)  # 浅复制
    
    print(f"原始对象: {person1}")
    print(f"浅复制对象: {person2}")
    print(f"person1 is person2: {person1 is person2}")
    print(f"person1.hobbies is person2.hobbies: {person1.hobbies is person2.hobbies}")
    
    # 修改嵌套属性 / Modify nested attribute
    person1.hobbies.append("编程")
    print(f"修改后原始对象: {person1}")
    print(f"修改后浅复制对象: {person2}")  # 共享hobbies列表

def deep_copy_examples():
    """深复制示例 / Deep Copy Examples"""
    print("\n=== 深复制示例 Deep Copy Examples ===")
    
    print("1. 使用copy.deepcopy():")
    original = [1, 2, [3, 4, [5, 6]], {"key": "value"}]
    deep_copy_obj = copy.deepcopy(original)
    
    print(f"原始列表: {original}")
    print(f"深复制列表: {deep_copy_obj}")
    print(f"original is deep_copy_obj: {original is deep_copy_obj}")
    print(f"original[2] is deep_copy_obj[2]: {original[2] is deep_copy_obj[2]}")
    print(f"original[2][2] is deep_copy_obj[2][2]: {original[2][2] is deep_copy_obj[2][2]}")
    print(f"original[3] is deep_copy_obj[3]: {original[3] is deep_copy_obj[3]}")
    
    print("\n2. 深复制的独立性:")
    # 修改原始对象的嵌套结构 / Modify nested structure of original
    original[2].append(7)
    original[2][2].append(8)
    original[3]["new_key"] = "new_value"
    
    print(f"修改后原始列表: {original}")
    print(f"深复制列表 (未受影响): {deep_copy_obj}")
    
    print("\n3. 深复制自定义对象:")
    
    class Student:
        def __init__(self, name, grades):
            self.name = name
            self.grades = grades
            self.friends = []
        
        def add_friend(self, friend):
            self.friends.append(friend)
        
        def __repr__(self):
            return f"Student('{self.name}', grades={self.grades}, friends={len(self.friends)})"
    
    student1 = Student("Alice", {"math": 90, "english": 85})
    student2 = Student("Bob", {"math": 88, "english": 92})
    
    student1.add_friend(student2)
    student2.add_friend(student1)
    
    print(f"原始学生: {student1}")
    print(f"朋友: {student1.friends}")
    
    # 深复制 / Deep copy
    student1_copy = copy.deepcopy(student1)
    
    print(f"深复制学生: {student1_copy}")
    print(f"student1 is student1_copy: {student1 is student1_copy}")
    print(f"student1.grades is student1_copy.grades: {student1.grades is student1_copy.grades}")
    print(f"student1.friends is student1_copy.friends: {student1.friends is student1_copy.friends}")
    
    # 修改原始对象 / Modify original object
    student1.grades["science"] = 95
    print(f"修改后原始学生成绩: {student1.grades}")
    print(f"深复制学生成绩 (未受影响): {student1_copy.grades}")

def copy_with_mutable_defaults():
    """可变默认参数的复制陷阱 / Copy Pitfalls with Mutable Defaults"""
    print("\n=== 可变默认参数陷阱 Mutable Default Arguments Pitfall ===")
    
    print("1. 错误的做法 - 可变默认参数:")
    
    def bad_function(item, target_list=[]):
        """错误: 使用可变对象作为默认参数"""
        target_list.append(item)
        return target_list
    
    result1 = bad_function("first")
    result2 = bad_function("second")
    result3 = bad_function("third")
    
    print(f"第一次调用: {result1}")
    print(f"第二次调用: {result2}")  # 包含之前的元素!
    print(f"第三次调用: {result3}")  # 包含所有之前的元素!
    
    print("\n2. 正确的做法 - 使用None作为默认值:")
    
    def good_function(item, target_list=None):
        """正确: 使用None作为默认参数"""
        if target_list is None:
            target_list = []  # 每次创建新列表
        target_list.append(item)
        return target_list
    
    result1 = good_function("first")
    result2 = good_function("second")
    result3 = good_function("third")
    
    print(f"第一次调用: {result1}")
    print(f"第二次调用: {result2}")
    print(f"第三次调用: {result3}")
    
    print("\n3. 使用copy.copy()的解决方案:")
    
    def copy_function(item, target_list=None):
        """使用copy创建独立副本"""
        if target_list is None:
            target_list = []
        else:
            target_list = copy.copy(target_list)  # 创建副本
        
        target_list.append(item)
        return target_list
    
    base_list = ["base"]
    result1 = copy_function("item1", base_list)
    result2 = copy_function("item2", base_list)
    
    print(f"基础列表: {base_list}")
    print(f"结果1: {result1}")
    print(f"结果2: {result2}")

def custom_copy_behavior():
    """自定义复制行为 / Custom Copy Behavior"""
    print("\n=== 自定义复制行为 Custom Copy Behavior ===")
    
    class CustomCopyClass:
        """演示自定义复制行为的类"""
        
        def __init__(self, name, data):
            self.name = name
            self.data = data
            self.copy_count = 0
        
        def __copy__(self):
            """自定义浅复制行为"""
            print(f"执行 {self.name} 的浅复制")
            new_obj = CustomCopyClass(f"{self.name}_copy", self.data)
            new_obj.copy_count = self.copy_count + 1
            return new_obj
        
        def __deepcopy__(self, memo):
            """自定义深复制行为"""
            print(f"执行 {self.name} 的深复制")
            new_data = copy.deepcopy(self.data, memo)
            new_obj = CustomCopyClass(f"{self.name}_deepcopy", new_data)
            new_obj.copy_count = self.copy_count + 1
            return new_obj
        
        def __repr__(self):
            return f"CustomCopyClass('{self.name}', {self.data}, copies={self.copy_count})"
    
    print("1. 自定义复制方法:")
    original = CustomCopyClass("original", [1, 2, [3, 4]])
    
    print(f"原始对象: {original}")
    
    # 浅复制 / Shallow copy
    shallow = copy.copy(original)
    print(f"浅复制对象: {shallow}")
    
    # 深复制 / Deep copy
    deep = copy.deepcopy(original)
    print(f"深复制对象: {deep}")
    
    print("\n2. 验证复制独立性:")
    original.data[2].append(5)
    print(f"修改后原始对象: {original}")
    print(f"浅复制对象: {shallow}")
    print(f"深复制对象: {deep}")

def performance_comparison():
    """性能比较 / Performance Comparison"""
    print("\n=== 性能比较 Performance Comparison ===")
    
    import time
    
    # 创建测试数据 / Create test data
    large_list = []
    for i in range(1000):
        large_list.append([j for j in range(100)])
    
    print("1. 复制性能测试 (1000个包含100个元素的列表):")
    
    # 测试赋值 / Test assignment
    start_time = time.time()
    assigned = large_list
    assignment_time = time.time() - start_time
    print(f"赋值时间: {assignment_time:.6f} 秒")
    
    # 测试浅复制 / Test shallow copy
    start_time = time.time()
    shallow = copy.copy(large_list)
    shallow_time = time.time() - start_time
    print(f"浅复制时间: {shallow_time:.6f} 秒")
    
    # 测试深复制 / Test deep copy
    start_time = time.time()
    deep = copy.deepcopy(large_list)
    deep_time = time.time() - start_time
    print(f"深复制时间: {deep_time:.6f} 秒")
    
    print(f"\n性能比较:")
    print(f"浅复制 vs 赋值: {shallow_time/assignment_time:.1f}x 慢")
    print(f"深复制 vs 浅复制: {deep_time/shallow_time:.1f}x 慢")
    print(f"深复制 vs 赋值: {deep_time/assignment_time:.1f}x 慢")

def practical_copy_scenarios():
    """实际应用场景 / Practical Copy Scenarios"""
    print("\n=== 实际应用场景 Practical Copy Scenarios ===")
    
    print("1. 配置对象的复制:")
    
    class Config:
        """配置类"""
        def __init__(self):
            self.database = {
                "host": "localhost",
                "port": 5432,
                "settings": {"timeout": 30, "pool_size": 10}
            }
            self.features = ["feature1", "feature2"]
        
        def get_test_config(self):
            """获取测试配置 - 使用深复制避免污染原配置"""
            test_config = copy.deepcopy(self)
            test_config.database["host"] = "test-db"
            test_config.database["settings"]["timeout"] = 5
            test_config.features.append("test_feature")
            return test_config
        
        def __repr__(self):
            return f"Config(db_host='{self.database['host']}', features={self.features})"
    
    prod_config = Config()
    test_config = prod_config.get_test_config()
    
    print(f"生产配置: {prod_config}")
    print(f"测试配置: {test_config}")
    
    print("\n2. 数据处理管道:")
    
    def process_data_pipeline(data, operations):
        """数据处理管道 - 每个操作都在数据副本上进行"""
        current_data = copy.deepcopy(data)  # 保护原始数据
        
        for i, operation in enumerate(operations):
            print(f"  步骤 {i+1}: {operation.__name__}")
            current_data = operation(current_data)
        
        return current_data
    
    def add_prefix(data):
        return [f"prefix_{item}" for item in data]
    
    def filter_long_items(data):
        return [item for item in data if len(str(item)) > 10]
    
    def convert_to_upper(data):
        return [str(item).upper() for item in data]
    
    original_data = ["apple", "banana", "cherry", "date"]
    operations = [add_prefix, filter_long_items, convert_to_upper]
    
    print(f"原始数据: {original_data}")
    processed_data = process_data_pipeline(original_data, operations)
    print(f"处理后数据: {processed_data}")
    print(f"原始数据未变: {original_data}")

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python对象和深浅复制学习示例")
    print("Python Object and Deep/Shallow Copy Learning Examples")
    print("=" * 60)
    
    object_identity_and_equality()
    shallow_copy_examples()
    deep_copy_examples()
    copy_with_mutable_defaults()
    custom_copy_behavior()
    performance_comparison()
    practical_copy_scenarios()
    
    print("\n" + "=" * 60)
    print("对象和深浅复制学习完成！Object and copy learning completed!")
