#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件输入输出 (File Input/Output) - Python文件操作
File Input/Output - Python File Operations

文件操作是程序与外部数据交互的重要方式，包括读取、写入、创建和删除文件。
File operations are important ways for programs to interact with external data, including reading, writing, creating, and deleting files.
"""

import os
import json
import csv
from pathlib import Path

def basic_file_operations():
    """基本文件操作 / Basic File Operations"""
    print("=== 基本文件操作 Basic File Operations ===")
    
    # 1. 写入文件 / Write to file
    print("1. 写入文件:")
    
    # 创建并写入文本文件 / Create and write text file
    with open('example.txt', 'w', encoding='utf-8') as file:
        file.write('Hello, World!\n')
        file.write('这是第二行\n')
        file.write('This is the third line\n')
    print("文件写入完成")
    
    # 2. 读取文件 / Read file
    print("\n2. 读取文件:")
    
    # 读取整个文件 / Read entire file
    with open('example.txt', 'r', encoding='utf-8') as file:
        content = file.read()
        print("文件内容:")
        print(content)
    
    # 逐行读取 / Read line by line
    print("逐行读取:")
    with open('example.txt', 'r', encoding='utf-8') as file:
        for line_num, line in enumerate(file, 1):
            print(f"第{line_num}行: {line.strip()}")
    
    # 3. 追加内容 / Append content
    print("\n3. 追加内容:")
    with open('example.txt', 'a', encoding='utf-8') as file:
        file.write('追加的内容\n')
        file.write('Appended content\n')
    
    # 验证追加结果 / Verify append result
    with open('example.txt', 'r', encoding='utf-8') as file:
        lines = file.readlines()
        print(f"文件现在有 {len(lines)} 行")
        print("最后两行:")
        for line in lines[-2:]:
            print(f"  {line.strip()}")

def file_reading_methods():
    """文件读取方法 / File Reading Methods"""
    print("\n=== 文件读取方法 File Reading Methods ===")
    
    # 创建测试文件 / Create test file
    test_content = """第一行内容
第二行内容
第三行内容
第四行内容
第五行内容"""
    
    with open('test_read.txt', 'w', encoding='utf-8') as file:
        file.write(test_content)
    
    print("1. read() - 读取整个文件:")
    with open('test_read.txt', 'r', encoding='utf-8') as file:
        content = file.read()
        print(f"内容长度: {len(content)} 字符")
        print("内容预览:", repr(content[:20]) + "...")
    
    print("\n2. readline() - 逐行读取:")
    with open('test_read.txt', 'r', encoding='utf-8') as file:
        line1 = file.readline()
        line2 = file.readline()
        print(f"第一行: {line1.strip()}")
        print(f"第二行: {line2.strip()}")
    
    print("\n3. readlines() - 读取所有行到列表:")
    with open('test_read.txt', 'r', encoding='utf-8') as file:
        lines = file.readlines()
        print(f"总行数: {len(lines)}")
        for i, line in enumerate(lines, 1):
            print(f"  行{i}: {line.strip()}")
    
    print("\n4. 使用for循环读取 (推荐):")
    with open('test_read.txt', 'r', encoding='utf-8') as file:
        for line_num, line in enumerate(file, 1):
            print(f"  行{line_num}: {line.strip()}")

def file_writing_methods():
    """文件写入方法 / File Writing Methods"""
    print("\n=== 文件写入方法 File Writing Methods ===")
    
    # 1. write() 方法 / write() method
    print("1. write() 方法:")
    with open('write_test.txt', 'w', encoding='utf-8') as file:
        file.write('使用write()方法写入\n')
        file.write('Write using write() method\n')
        
        # 写入数字需要转换为字符串 / Numbers need to be converted to strings
        number = 42
        file.write(f'数字: {number}\n')
    
    # 2. writelines() 方法 / writelines() method
    print("2. writelines() 方法:")
    lines = [
        '第一行\n',
        '第二行\n',
        '第三行\n'
    ]
    
    with open('writelines_test.txt', 'w', encoding='utf-8') as file:
        file.writelines(lines)
    
    # 3. print() 函数写入文件 / print() function to file
    print("3. 使用print()函数写入文件:")
    with open('print_test.txt', 'w', encoding='utf-8') as file:
        print('使用print()函数', file=file)
        print('Print to file', file=file)
        print('数字:', 123, '字符串:', 'hello', file=file)
    
    # 验证写入结果 / Verify writing results
    print("\n验证写入结果:")
    for filename in ['write_test.txt', 'writelines_test.txt', 'print_test.txt']:
        print(f"\n{filename} 内容:")
        with open(filename, 'r', encoding='utf-8') as file:
            print(file.read())

def file_modes_and_encoding():
    """文件模式和编码 / File Modes and Encoding"""
    print("\n=== 文件模式和编码 File Modes and Encoding ===")
    
    print("常用文件模式:")
    modes = {
        'r': '只读模式 (默认)',
        'w': '写入模式 (覆盖原文件)',
        'a': '追加模式',
        'x': '独占创建模式 (文件不存在时创建)',
        'r+': '读写模式',
        'w+': '读写模式 (覆盖原文件)',
        'a+': '读写模式 (追加)',
        'rb': '二进制只读模式',
        'wb': '二进制写入模式',
        'ab': '二进制追加模式'
    }
    
    for mode, description in modes.items():
        print(f"  {mode:3}: {description}")
    
    # 演示不同编码 / Demonstrate different encodings
    print("\n编码示例:")
    
    # UTF-8 编码 (推荐) / UTF-8 encoding (recommended)
    chinese_text = "你好，世界！Hello, World!"
    
    with open('utf8_test.txt', 'w', encoding='utf-8') as file:
        file.write(chinese_text)
    
    # 读取并验证 / Read and verify
    with open('utf8_test.txt', 'r', encoding='utf-8') as file:
        content = file.read()
        print(f"UTF-8 内容: {content}")
    
    # 二进制模式示例 / Binary mode example
    print("\n二进制模式示例:")
    binary_data = b'Binary data: \x00\x01\x02\x03'
    
    with open('binary_test.bin', 'wb') as file:
        file.write(binary_data)
    
    with open('binary_test.bin', 'rb') as file:
        read_data = file.read()
        print(f"二进制数据: {read_data}")

def working_with_paths():
    """路径操作 / Working with Paths"""
    print("\n=== 路径操作 Working with Paths ===")
    
    # 使用 os.path / Using os.path
    print("1. 使用 os.path:")
    current_dir = os.getcwd()
    print(f"当前目录: {current_dir}")
    
    file_path = os.path.join(current_dir, 'test_file.txt')
    print(f"文件路径: {file_path}")
    
    print(f"目录名: {os.path.dirname(file_path)}")
    print(f"文件名: {os.path.basename(file_path)}")
    print(f"文件扩展名: {os.path.splitext(file_path)[1]}")
    
    # 使用 pathlib (推荐) / Using pathlib (recommended)
    print("\n2. 使用 pathlib (推荐):")
    from pathlib import Path
    
    current_path = Path.cwd()
    print(f"当前路径: {current_path}")
    
    file_path = current_path / 'test_file.txt'
    print(f"文件路径: {file_path}")
    
    print(f"父目录: {file_path.parent}")
    print(f"文件名: {file_path.name}")
    print(f"文件扩展名: {file_path.suffix}")
    print(f"不含扩展名的文件名: {file_path.stem}")
    
    # 检查路径是否存在 / Check if path exists
    print(f"路径是否存在: {file_path.exists()}")
    print(f"是否为文件: {file_path.is_file()}")
    print(f"是否为目录: {file_path.is_dir()}")

def directory_operations():
    """目录操作 / Directory Operations"""
    print("\n=== 目录操作 Directory Operations ===")
    
    # 创建目录 / Create directory
    test_dir = Path('test_directory')
    
    if not test_dir.exists():
        test_dir.mkdir()
        print(f"创建目录: {test_dir}")
    
    # 创建嵌套目录 / Create nested directories
    nested_dir = test_dir / 'nested' / 'deep'
    nested_dir.mkdir(parents=True, exist_ok=True)
    print(f"创建嵌套目录: {nested_dir}")
    
    # 在目录中创建文件 / Create files in directory
    for i in range(3):
        file_path = test_dir / f'file_{i}.txt'
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(f'这是文件 {i} 的内容\n')
    
    # 列出目录内容 / List directory contents
    print(f"\n{test_dir} 目录内容:")
    for item in test_dir.iterdir():
        if item.is_file():
            print(f"  文件: {item.name}")
        elif item.is_dir():
            print(f"  目录: {item.name}")
    
    # 递归列出所有文件 / Recursively list all files
    print(f"\n递归列出所有 .txt 文件:")
    for txt_file in test_dir.rglob('*.txt'):
        print(f"  {txt_file}")

def structured_data_files():
    """结构化数据文件 / Structured Data Files"""
    print("\n=== 结构化数据文件 Structured Data Files ===")
    
    # 1. JSON 文件操作 / JSON file operations
    print("1. JSON 文件操作:")
    
    # 创建示例数据 / Create sample data
    data = {
        'name': '张三',
        'age': 25,
        'city': '北京',
        'hobbies': ['读书', '游泳', '编程'],
        'is_student': False
    }
    
    # 写入 JSON 文件 / Write JSON file
    with open('data.json', 'w', encoding='utf-8') as file:
        json.dump(data, file, ensure_ascii=False, indent=2)
    print("JSON 数据已写入文件")
    
    # 读取 JSON 文件 / Read JSON file
    with open('data.json', 'r', encoding='utf-8') as file:
        loaded_data = json.load(file)
        print(f"从文件读取的数据: {loaded_data}")
    
    # 2. CSV 文件操作 / CSV file operations
    print("\n2. CSV 文件操作:")
    
    # 写入 CSV 文件 / Write CSV file
    students = [
        ['姓名', '年龄', '成绩'],
        ['张三', 20, 85],
        ['李四', 21, 92],
        ['王五', 19, 78]
    ]
    
    with open('students.csv', 'w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerows(students)
    print("CSV 数据已写入文件")
    
    # 读取 CSV 文件 / Read CSV file
    print("从 CSV 文件读取数据:")
    with open('students.csv', 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        for row_num, row in enumerate(reader):
            print(f"  行{row_num + 1}: {row}")
    
    # 使用 DictReader 读取 CSV / Read CSV using DictReader
    print("\n使用 DictReader 读取 CSV:")
    with open('students.csv', 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            print(f"  {row}")

def error_handling_in_files():
    """文件操作中的错误处理 / Error Handling in File Operations"""
    print("\n=== 文件操作错误处理 Error Handling in File Operations ===")
    
    # 1. 文件不存在错误 / File not found error
    print("1. 处理文件不存在错误:")
    try:
        with open('nonexistent_file.txt', 'r') as file:
            content = file.read()
    except FileNotFoundError:
        print("  错误: 文件不存在")
    except PermissionError:
        print("  错误: 没有权限访问文件")
    except Exception as e:
        print(f"  其他错误: {e}")
    
    # 2. 权限错误处理 / Permission error handling
    print("\n2. 安全的文件操作:")
    
    def safe_read_file(filename):
        """安全读取文件的函数"""
        try:
            with open(filename, 'r', encoding='utf-8') as file:
                return file.read()
        except FileNotFoundError:
            print(f"  文件 {filename} 不存在")
            return None
        except PermissionError:
            print(f"  没有权限读取文件 {filename}")
            return None
        except UnicodeDecodeError:
            print(f"  文件 {filename} 编码错误")
            return None
        except Exception as e:
            print(f"  读取文件 {filename} 时发生未知错误: {e}")
            return None
    
    # 测试安全读取函数 / Test safe read function
    content = safe_read_file('example.txt')
    if content:
        print(f"  成功读取文件，内容长度: {len(content)}")
    
    content = safe_read_file('nonexistent.txt')
    if content is None:
        print("  文件读取失败")

def file_best_practices():
    """文件操作最佳实践 / File Operation Best Practices"""
    print("\n=== 文件操作最佳实践 File Operation Best Practices ===")
    
    print("最佳实践建议:")
    print("1. 总是使用 with 语句确保文件正确关闭")
    print("2. 明确指定文件编码 (通常使用 utf-8)")
    print("3. 使用 pathlib 而不是 os.path 进行路径操作")
    print("4. 处理可能的异常情况")
    print("5. 对于大文件，考虑逐行处理而不是一次性读取")
    print("6. 使用适当的文件模式")
    print("7. 处理二进制文件时使用二进制模式")
    
    # 示例：处理大文件 / Example: handling large files
    print("\n大文件处理示例:")
    
    def process_large_file(filename):
        """处理大文件的示例函数"""
        try:
            line_count = 0
            with open(filename, 'r', encoding='utf-8') as file:
                for line in file:
                    line_count += 1
                    # 处理每一行
                    if line_count % 1000 == 0:
                        print(f"  已处理 {line_count} 行")
            
            print(f"  文件总行数: {line_count}")
            
        except Exception as e:
            print(f"  处理文件时出错: {e}")
    
    # 创建一个测试文件 / Create a test file
    with open('large_test.txt', 'w', encoding='utf-8') as file:
        for i in range(5000):
            file.write(f'这是第 {i + 1} 行内容\n')
    
    print("处理大文件:")
    process_large_file('large_test.txt')

def cleanup_files():
    """清理测试文件 / Cleanup Test Files"""
    print("\n=== 清理测试文件 Cleanup Test Files ===")
    
    # 要删除的文件列表 / List of files to delete
    files_to_delete = [
        'example.txt', 'test_read.txt', 'write_test.txt', 
        'writelines_test.txt', 'print_test.txt', 'utf8_test.txt',
        'binary_test.bin', 'data.json', 'students.csv', 'large_test.txt'
    ]
    
    # 要删除的目录 / Directory to delete
    import shutil
    
    for filename in files_to_delete:
        try:
            if os.path.exists(filename):
                os.remove(filename)
                print(f"删除文件: {filename}")
        except Exception as e:
            print(f"删除文件 {filename} 失败: {e}")
    
    # 删除测试目录 / Delete test directory
    test_dir = Path('test_directory')
    if test_dir.exists():
        shutil.rmtree(test_dir)
        print(f"删除目录: {test_dir}")

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python文件输入输出学习示例")
    print("Python File I/O Learning Examples")
    print("=" * 50)
    
    basic_file_operations()
    file_reading_methods()
    file_writing_methods()
    file_modes_and_encoding()
    working_with_paths()
    directory_operations()
    structured_data_files()
    error_handling_in_files()
    file_best_practices()
    
    print("\n" + "=" * 50)
    print("文件输入输出学习完成！File I/O learning completed!")
    
    # 清理测试文件 / Cleanup test files
    cleanup_files()
