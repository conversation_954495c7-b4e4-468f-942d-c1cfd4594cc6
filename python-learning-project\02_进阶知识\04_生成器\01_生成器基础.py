#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成器 (Generators) - Python惰性求值
Generators - Python Lazy Evaluation

生成器是Python中用于创建迭代器的简洁方式，支持惰性求值和内存高效的数据处理。
Generators are a concise way to create iterators in Python, supporting lazy evaluation and memory-efficient data processing.
"""

import sys
import time
from typing import Generator, Iterator

def basic_generator_concept():
    """生成器基本概念 / Basic Generator Concept"""
    print("=== 生成器基本概念 Basic Generator Concept ===")
    
    # 普通函数 vs 生成器函数 / Regular function vs Generator function
    def regular_function():
        """普通函数返回列表"""
        result = []
        for i in range(5):
            result.append(i ** 2)
        return result
    
    def generator_function():
        """生成器函数使用yield"""
        for i in range(5):
            print(f"生成 {i}^2")
            yield i ** 2
    
    print("普通函数:")
    regular_result = regular_function()
    print(f"结果: {regular_result}")
    print(f"类型: {type(regular_result)}")
    
    print("\n生成器函数:")
    gen = generator_function()
    print(f"生成器对象: {gen}")
    print(f"类型: {type(gen)}")
    
    print("\n逐个获取生成器的值:")
    for value in gen:
        print(f"获得: {value}")

def yield_keyword():
    """yield关键字详解 / Yield Keyword Explained"""
    print("\n=== yield关键字详解 Yield Keyword Explained ===")
    
    def countdown(n):
        """倒计时生成器"""
        print(f"开始倒计时从 {n}")
        while n > 0:
            print(f"yield前: n = {n}")
            yield n
            print(f"yield后: n = {n}")
            n -= 1
        print("倒计时结束")
    
    print("创建生成器:")
    counter = countdown(3)
    
    print("\n手动调用next():")
    print(f"第一次next(): {next(counter)}")
    print(f"第二次next(): {next(counter)}")
    print(f"第三次next(): {next(counter)}")
    
    try:
        print(f"第四次next(): {next(counter)}")
    except StopIteration:
        print("生成器已耗尽，抛出StopIteration异常")

def generator_expressions():
    """生成器表达式 / Generator Expressions"""
    print("\n=== 生成器表达式 Generator Expressions ===")
    
    # 列表推导式 vs 生成器表达式 / List comprehension vs Generator expression
    list_comp = [x**2 for x in range(10)]
    gen_exp = (x**2 for x in range(10))
    
    print(f"列表推导式: {list_comp}")
    print(f"列表推导式类型: {type(list_comp)}")
    print(f"列表推导式内存: {sys.getsizeof(list_comp)} 字节")
    
    print(f"\n生成器表达式: {gen_exp}")
    print(f"生成器表达式类型: {type(gen_exp)}")
    print(f"生成器表达式内存: {sys.getsizeof(gen_exp)} 字节")
    
    print("\n生成器表达式的值:")
    for value in gen_exp:
        print(value, end=" ")
    print()
    
    # 生成器表达式的实际应用 / Practical use of generator expressions
    print("\n文本处理示例:")
    lines = ["  hello world  ", "  PYTHON  ", "  programming  "]
    
    # 清理和处理文本 / Clean and process text
    cleaned = (line.strip().lower() for line in lines if line.strip())
    word_lengths = (len(line) for line in cleaned)
    
    print("处理后的长度:", list(word_lengths))

def memory_efficiency():
    """内存效率对比 / Memory Efficiency Comparison"""
    print("\n=== 内存效率对比 Memory Efficiency Comparison ===")
    
    def create_large_list(n):
        """创建大列表"""
        return [i for i in range(n)]
    
    def create_large_generator(n):
        """创建大生成器"""
        for i in range(n):
            yield i
    
    n = 100000
    
    # 测试内存使用 / Test memory usage
    large_list = create_large_list(n)
    large_gen = create_large_generator(n)
    
    print(f"大列表({n}个元素)内存: {sys.getsizeof(large_list)} 字节")
    print(f"大生成器({n}个元素)内存: {sys.getsizeof(large_gen)} 字节")
    
    # 测试创建时间 / Test creation time
    start_time = time.time()
    list_result = create_large_list(n)
    list_time = time.time() - start_time
    
    start_time = time.time()
    gen_result = create_large_generator(n)
    gen_time = time.time() - start_time
    
    print(f"\n创建大列表耗时: {list_time:.6f}秒")
    print(f"创建大生成器耗时: {gen_time:.6f}秒")

    if gen_time > 0:
        print(f"生成器比列表快 {list_time/gen_time:.1f} 倍")
    else:
        print("生成器创建几乎瞬间完成，比列表快很多倍")

def generator_methods():
    """生成器方法 / Generator Methods"""
    print("\n=== 生成器方法 Generator Methods ===")
    
    def interactive_generator():
        """交互式生成器"""
        print("生成器启动")
        value = None
        
        while True:
            try:
                print(f"当前值: {value}")
                # 接收外部发送的值 / Receive value sent from outside
                received = yield value
                
                if received is not None:
                    value = received
                    print(f"接收到: {received}")
                else:
                    value = (value or 0) + 1
                    
            except GeneratorExit:
                print("生成器正在关闭")
                break
            except Exception as e:
                print(f"生成器捕获异常: {e}")
                value = -1
    
    print("创建交互式生成器:")
    gen = interactive_generator()
    
    # 启动生成器 / Start generator
    print(f"启动: {next(gen)}")
    
    # 发送值 / Send values
    print(f"发送10: {gen.send(10)}")
    print(f"发送20: {gen.send(20)}")
    
    # 发送None（相当于next()）/ Send None (equivalent to next())
    print(f"发送None: {gen.send(None)}")
    
    # 抛出异常 / Throw exception
    try:
        gen.throw(ValueError, "测试异常")
        print(f"异常处理后: {next(gen)}")
    except StopIteration:
        pass
    
    # 关闭生成器 / Close generator
    gen.close()

def practical_generators():
    """实用生成器示例 / Practical Generator Examples"""
    print("\n=== 实用生成器示例 Practical Generator Examples ===")
    
    # 1. 文件读取生成器 / File reading generator
    def read_lines(filename):
        """逐行读取文件的生成器"""
        try:
            with open(filename, 'r', encoding='utf-8') as file:
                for line_num, line in enumerate(file, 1):
                    yield line_num, line.strip()
        except FileNotFoundError:
            print(f"文件 {filename} 不存在")
            return
    
    # 2. 斐波那契数列生成器 / Fibonacci sequence generator
    def fibonacci():
        """无限斐波那契数列生成器"""
        a, b = 0, 1
        while True:
            yield a
            a, b = b, a + b
    
    # 3. 素数生成器 / Prime number generator
    def primes():
        """素数生成器"""
        def is_prime(n):
            if n < 2:
                return False
            for i in range(2, int(n**0.5) + 1):
                if n % i == 0:
                    return False
            return True
        
        n = 2
        while True:
            if is_prime(n):
                yield n
            n += 1
    
    # 4. 批处理生成器 / Batch processing generator
    def batch_processor(data, batch_size):
        """将数据分批处理的生成器"""
        for i in range(0, len(data), batch_size):
            yield data[i:i + batch_size]
    
    print("1. 斐波那契数列前10项:")
    fib_gen = fibonacci()
    fib_numbers = [next(fib_gen) for _ in range(10)]
    print(fib_numbers)
    
    print("\n2. 前10个素数:")
    prime_gen = primes()
    prime_numbers = [next(prime_gen) for _ in range(10)]
    print(prime_numbers)
    
    print("\n3. 批处理示例:")
    data = list(range(1, 21))  # 1到20的数字
    print(f"原始数据: {data}")
    
    for batch_num, batch in enumerate(batch_processor(data, 5), 1):
        print(f"批次{batch_num}: {batch}")
    
    # 5. 数据流水线 / Data pipeline
    def data_pipeline():
        """数据处理流水线示例"""
        # 数据源 / Data source
        def data_source():
            for i in range(1, 11):
                yield i
        
        # 数据转换 / Data transformation
        def square_transform(data_gen):
            for value in data_gen:
                yield value ** 2
        
        def filter_even(data_gen):
            for value in data_gen:
                if value % 2 == 0:
                    yield value
        
        # 构建流水线 / Build pipeline
        source = data_source()
        squared = square_transform(source)
        filtered = filter_even(squared)
        
        return list(filtered)
    
    print("\n4. 数据流水线(1-10的平方中的偶数):")
    pipeline_result = data_pipeline()
    print(pipeline_result)

def generator_patterns():
    """生成器模式 / Generator Patterns"""
    print("\n=== 生成器模式 Generator Patterns ===")
    
    # 1. 生成器装饰器 / Generator decorator
    def generator_timer(gen_func):
        """为生成器添加计时功能的装饰器"""
        def wrapper(*args, **kwargs):
            gen = gen_func(*args, **kwargs)
            start_time = time.time()
            
            try:
                while True:
                    value = next(gen)
                    yield value
            except StopIteration:
                end_time = time.time()
                print(f"生成器 {gen_func.__name__} 总耗时: {end_time - start_time:.6f}秒")
        
        return wrapper
    
    # 2. 生成器组合 / Generator composition
    def chain_generators(*generators):
        """链接多个生成器"""
        for gen in generators:
            yield from gen
    
    def zip_generators(*generators):
        """并行处理多个生成器"""
        iterators = [iter(gen) for gen in generators]
        while iterators:
            values = []
            finished = []
            
            for i, it in enumerate(iterators):
                try:
                    values.append(next(it))
                except StopIteration:
                    finished.append(i)
            
            if finished:
                # 移除已完成的迭代器 / Remove finished iterators
                for i in reversed(finished):
                    iterators.pop(i)
                if not iterators:
                    break
            
            if values:
                yield tuple(values)
    
    @generator_timer
    def slow_generator(n):
        """慢速生成器"""
        for i in range(n):
            time.sleep(0.01)  # 模拟耗时操作
            yield i
    
    print("1. 生成器装饰器:")
    slow_gen = slow_generator(5)
    result = list(slow_gen)
    print(f"结果: {result}")
    
    print("\n2. 生成器链接:")
    gen1 = (x for x in range(3))
    gen2 = (x for x in range(3, 6))
    gen3 = (x for x in range(6, 9))
    
    chained = chain_generators(gen1, gen2, gen3)
    print(f"链接结果: {list(chained)}")
    
    print("\n3. 生成器并行:")
    gen_a = (f"A{i}" for i in range(3))
    gen_b = (f"B{i}" for i in range(4))
    gen_c = (f"C{i}" for i in range(2))
    
    zipped = zip_generators(gen_a, gen_b, gen_c)
    print("并行结果:")
    for values in zipped:
        print(values)

def performance_tips():
    """性能优化技巧 / Performance Tips"""
    print("\n=== 性能优化技巧 Performance Tips ===")
    
    # 1. 避免不必要的计算 / Avoid unnecessary computation
    def inefficient_generator(n):
        """低效的生成器"""
        expensive_list = [i**2 for i in range(n)]  # 预先计算所有值
        for value in expensive_list:
            yield value
    
    def efficient_generator(n):
        """高效的生成器"""
        for i in range(n):
            yield i**2  # 按需计算
    
    # 2. 使用yield from / Use yield from
    def manual_delegation(generators):
        """手动委托"""
        for gen in generators:
            for value in gen:
                yield value
    
    def yield_from_delegation(generators):
        """使用yield from委托"""
        for gen in generators:
            yield from gen
    
    print("性能建议:")
    print("1. 使用生成器而不是列表来处理大数据集")
    print("2. 使用yield from进行生成器委托")
    print("3. 避免在生成器中预先计算所有值")
    print("4. 合理使用生成器表达式")
    
    # 测试yield from性能 / Test yield from performance
    test_gens = [(i for i in range(1000)) for _ in range(10)]
    
    start_time = time.time()
    list(manual_delegation(test_gens))
    manual_time = time.time() - start_time
    
    test_gens = [(i for i in range(1000)) for _ in range(10)]
    start_time = time.time()
    list(yield_from_delegation(test_gens))
    yield_from_time = time.time() - start_time
    
    print(f"\n手动委托耗时: {manual_time:.6f}秒")
    print(f"yield from耗时: {yield_from_time:.6f}秒")

    if yield_from_time > 0 and manual_time > 0:
        print(f"yield from比手动委托快 {manual_time/yield_from_time:.2f} 倍")
    else:
        print("两种方法执行时间都很短，性能差异不明显")

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python生成器学习示例")
    print("Python Generators Learning Examples")
    print("=" * 50)
    
    basic_generator_concept()
    yield_keyword()
    generator_expressions()
    memory_efficiency()
    generator_methods()
    practical_generators()
    generator_patterns()
    performance_tips()
    
    print("\n" + "=" * 50)
    print("生成器学习完成！Generators learning completed!")
