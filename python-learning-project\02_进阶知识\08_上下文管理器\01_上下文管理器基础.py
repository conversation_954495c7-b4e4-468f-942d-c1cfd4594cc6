#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
上下文管理器 (Context Managers) - Python资源管理
Context Managers - Python Resource Management

上下文管理器是Python中用于确保资源正确获取和释放的机制，主要通过with语句使用。
Context managers are mechanisms in Python for ensuring proper acquisition and release of resources, primarily used with the with statement.
"""

import time
import threading
from contextlib import contextmanager, ExitStack, suppress
from typing import Any, Optional

def basic_with_statement():
    """基本with语句 / Basic With Statement"""
    print("=== 基本with语句 Basic With Statement ===")
    
    # 传统文件操作 / Traditional file operations
    print("1. 传统文件操作:")
    try:
        file = open('temp.txt', 'w')
        file.write('Hello, World!')
        file.close()
        print("文件写入成功（手动关闭）")
    except Exception as e:
        print(f"错误: {e}")
    
    # 使用with语句 / Using with statement
    print("\n2. 使用with语句:")
    try:
        with open('temp.txt', 'w') as file:
            file.write('Hello, Context Manager!')
        print("文件写入成功（自动关闭）")
    except Exception as e:
        print(f"错误: {e}")
    
    # 验证文件内容 / Verify file content
    try:
        with open('temp.txt', 'r') as file:
            content = file.read()
            print(f"文件内容: {content}")
    except FileNotFoundError:
        print("文件不存在")

def context_manager_protocol():
    """上下文管理器协议 / Context Manager Protocol"""
    print("\n=== 上下文管理器协议 Context Manager Protocol ===")
    
    class SimpleContextManager:
        """简单的上下文管理器"""
        
        def __init__(self, name):
            self.name = name
        
        def __enter__(self):
            """进入上下文时调用"""
            print(f"进入上下文: {self.name}")
            return self  # 返回给as后的变量
        
        def __exit__(self, exc_type, exc_value, traceback):
            """退出上下文时调用"""
            if exc_type is None:
                print(f"正常退出上下文: {self.name}")
            else:
                print(f"异常退出上下文: {self.name}")
                print(f"异常类型: {exc_type.__name__}")
                print(f"异常值: {exc_value}")
            
            # 返回False表示不抑制异常，True表示抑制异常
            return False
        
        def do_something(self):
            """执行某些操作"""
            print(f"在 {self.name} 中执行操作")
    
    print("1. 正常使用上下文管理器:")
    with SimpleContextManager("测试1") as cm:
        cm.do_something()
    
    print("\n2. 异常情况下的上下文管理器:")
    try:
        with SimpleContextManager("测试2") as cm:
            cm.do_something()
            raise ValueError("模拟异常")
    except ValueError as e:
        print(f"捕获异常: {e}")

def database_connection_manager():
    """数据库连接管理器示例 / Database Connection Manager Example"""
    print("\n=== 数据库连接管理器 Database Connection Manager ===")
    
    class DatabaseConnection:
        """模拟数据库连接"""
        
        def __init__(self, host, port):
            self.host = host
            self.port = port
            self.connected = False
        
        def connect(self):
            """连接数据库"""
            print(f"连接到数据库 {self.host}:{self.port}")
            self.connected = True
        
        def disconnect(self):
            """断开数据库连接"""
            if self.connected:
                print(f"断开数据库连接 {self.host}:{self.port}")
                self.connected = False
        
        def execute(self, query):
            """执行查询"""
            if not self.connected:
                raise RuntimeError("数据库未连接")
            print(f"执行查询: {query}")
            return f"查询结果: {query}"
    
    class DatabaseManager:
        """数据库管理器上下文管理器"""
        
        def __init__(self, host, port):
            self.connection = DatabaseConnection(host, port)
        
        def __enter__(self):
            self.connection.connect()
            return self.connection
        
        def __exit__(self, exc_type, exc_value, traceback):
            self.connection.disconnect()
            if exc_type:
                print(f"数据库操作异常: {exc_value}")
            return False
    
    print("使用数据库管理器:")
    with DatabaseManager("localhost", 5432) as db:
        result = db.execute("SELECT * FROM users")
        print(result)
    
    print("\n异常情况:")
    try:
        with DatabaseManager("localhost", 5432) as db:
            db.execute("SELECT * FROM users")
            raise Exception("数据库操作失败")
    except Exception as e:
        print(f"处理异常: {e}")

def contextlib_contextmanager():
    """使用contextlib.contextmanager装饰器 / Using contextlib.contextmanager Decorator"""
    print("\n=== contextlib.contextmanager装饰器 ===")
    
    @contextmanager
    def timer_context(name):
        """计时上下文管理器"""
        print(f"开始计时: {name}")
        start_time = time.time()
        
        try:
            yield start_time  # yield的值会传递给as后的变量
        finally:
            end_time = time.time()
            duration = end_time - start_time
            print(f"结束计时: {name}, 耗时: {duration:.4f}秒")
    
    @contextmanager
    def temporary_setting(setting_name, new_value):
        """临时设置上下文管理器"""
        # 模拟全局设置
        global_settings = {"debug": False, "verbose": True}
        
        old_value = global_settings.get(setting_name)
        print(f"临时设置 {setting_name}: {old_value} -> {new_value}")
        global_settings[setting_name] = new_value
        
        try:
            yield global_settings
        finally:
            global_settings[setting_name] = old_value
            print(f"恢复设置 {setting_name}: {new_value} -> {old_value}")
    
    @contextmanager
    def error_handler(error_message="操作失败"):
        """错误处理上下文管理器"""
        try:
            yield
        except Exception as e:
            print(f"{error_message}: {e}")
            # 抑制异常
            return
    
    print("1. 计时上下文管理器:")
    with timer_context("数据处理") as start_time:
        time.sleep(0.1)  # 模拟耗时操作
        print(f"操作开始时间: {start_time}")
    
    print("\n2. 临时设置上下文管理器:")
    with temporary_setting("debug", True) as settings:
        print(f"当前设置: {settings}")
    
    print("\n3. 错误处理上下文管理器:")
    with error_handler("计算错误"):
        result = 10 / 0  # 会引发异常，但被抑制
    
    print("程序继续执行")

def multiple_context_managers():
    """多个上下文管理器 / Multiple Context Managers"""
    print("\n=== 多个上下文管理器 Multiple Context Managers ===")
    
    @contextmanager
    def resource_manager(name):
        """资源管理器"""
        print(f"获取资源: {name}")
        try:
            yield name
        finally:
            print(f"释放资源: {name}")
    
    # 方法1: 嵌套with语句 / Method 1: Nested with statements
    print("1. 嵌套with语句:")
    with resource_manager("资源A"):
        with resource_manager("资源B"):
            with resource_manager("资源C"):
                print("使用所有资源")
    
    # 方法2: 单个with语句管理多个资源 / Method 2: Single with statement for multiple resources
    print("\n2. 单个with语句管理多个资源:")
    with resource_manager("资源1"), resource_manager("资源2"), resource_manager("资源3"):
        print("使用所有资源")
    
    # 方法3: 使用ExitStack / Method 3: Using ExitStack
    print("\n3. 使用ExitStack:")
    with ExitStack() as stack:
        res1 = stack.enter_context(resource_manager("动态资源1"))
        res2 = stack.enter_context(resource_manager("动态资源2"))
        
        # 可以动态添加更多资源
        if True:  # 某种条件
            res3 = stack.enter_context(resource_manager("条件资源"))
        
        print(f"使用资源: {res1}, {res2}")

def thread_lock_manager():
    """线程锁管理器 / Thread Lock Manager"""
    print("\n=== 线程锁管理器 Thread Lock Manager ===")
    
    class ThreadSafeCounter:
        """线程安全计数器"""
        
        def __init__(self):
            self._value = 0
            self._lock = threading.Lock()
        
        @contextmanager
        def lock_context(self):
            """锁上下文管理器"""
            print(f"线程 {threading.current_thread().name} 尝试获取锁")
            self._lock.acquire()
            try:
                print(f"线程 {threading.current_thread().name} 获得锁")
                yield
            finally:
                print(f"线程 {threading.current_thread().name} 释放锁")
                self._lock.release()
        
        def increment(self):
            """安全递增"""
            with self.lock_context():
                old_value = self._value
                time.sleep(0.01)  # 模拟处理时间
                self._value = old_value + 1
                print(f"计数器: {old_value} -> {self._value}")
        
        @property
        def value(self):
            return self._value
    
    # 测试线程安全
    counter = ThreadSafeCounter()
    
    def worker(counter, name):
        """工作线程"""
        for i in range(3):
            counter.increment()
    
    print("启动多个线程:")
    threads = []
    for i in range(2):
        thread = threading.Thread(target=worker, args=(counter, f"Worker-{i}"))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    print(f"最终计数器值: {counter.value}")

def practical_context_managers():
    """实用上下文管理器 / Practical Context Managers"""
    print("\n=== 实用上下文管理器 Practical Context Managers ===")
    
    @contextmanager
    def change_directory(path):
        """临时改变工作目录"""
        import os
        original_path = os.getcwd()
        try:
            os.chdir(path)
            print(f"切换到目录: {path}")
            yield path
        except OSError as e:
            print(f"无法切换到目录 {path}: {e}")
            yield original_path
        finally:
            os.chdir(original_path)
            print(f"恢复到原目录: {original_path}")
    
    @contextmanager
    def suppress_stdout():
        """抑制标准输出"""
        import sys
        from io import StringIO
        
        old_stdout = sys.stdout
        sys.stdout = StringIO()
        try:
            yield
        finally:
            sys.stdout = old_stdout
    
    @contextmanager
    def retry_context(max_attempts=3, delay=1):
        """重试上下文管理器"""
        for attempt in range(max_attempts):
            try:
                yield attempt + 1
                break  # 成功则跳出
            except Exception as e:
                if attempt == max_attempts - 1:
                    print(f"重试{max_attempts}次后仍然失败")
                    raise e
                else:
                    print(f"第{attempt + 1}次尝试失败: {e}, {delay}秒后重试")
                    time.sleep(delay)
    
    # 使用suppress抑制特定异常
    print("1. 使用suppress抑制异常:")
    with suppress(ZeroDivisionError):
        result = 10 / 0
        print("这行不会执行")
    print("异常被抑制，程序继续")
    
    # 使用重试上下文管理器
    print("\n2. 重试上下文管理器:")
    import random
    
    with retry_context(max_attempts=3, delay=0.1) as attempt:
        print(f"第{attempt}次尝试")
        if random.random() < 0.7:  # 70%概率失败
            raise Exception("随机失败")
        print("操作成功！")

def context_manager_best_practices():
    """上下文管理器最佳实践 / Context Manager Best Practices"""
    print("\n=== 最佳实践 Best Practices ===")
    
    print("1. 总是在__exit__中清理资源")
    print("2. 考虑异常情况下的资源释放")
    print("3. 使用contextlib.contextmanager简化实现")
    print("4. 合理使用ExitStack管理多个资源")
    print("5. 在多线程环境中注意线程安全")
    
    # 示例：资源泄漏检测
    @contextmanager
    def resource_leak_detector():
        """资源泄漏检测器"""
        import gc
        
        # 记录初始对象数量
        gc.collect()
        initial_objects = len(gc.get_objects())
        
        try:
            yield
        finally:
            # 检查对象数量变化
            gc.collect()
            final_objects = len(gc.get_objects())
            leaked = final_objects - initial_objects
            
            if leaked > 10:  # 阈值
                print(f"警告: 可能存在资源泄漏，新增对象: {leaked}")
            else:
                print(f"资源使用正常，新增对象: {leaked}")
    
    print("\n资源泄漏检测示例:")
    with resource_leak_detector():
        # 模拟一些操作
        data = [i for i in range(1000)]
        del data

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python上下文管理器学习示例")
    print("Python Context Managers Learning Examples")
    print("=" * 50)
    
    basic_with_statement()
    context_manager_protocol()
    database_connection_manager()
    contextlib_contextmanager()
    multiple_context_managers()
    thread_lock_manager()
    practical_context_managers()
    context_manager_best_practices()
    
    print("\n" + "=" * 50)
    print("上下文管理器学习完成！Context managers learning completed!")
    
    # 清理临时文件
    import os
    try:
        os.remove('temp.txt')
        print("清理临时文件完成")
    except FileNotFoundError:
        pass
