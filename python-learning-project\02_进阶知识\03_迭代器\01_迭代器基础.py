#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
迭代器 (Iterators) - Python迭代器协议和自定义迭代器
Iterators - Python Iterator Protocol and Custom Iterators

迭代器是Python中用于遍历数据集合的重要机制，理解迭代器协议对于编写高效的Python代码至关重要。
Iterators are an important mechanism in Python for traversing data collections. Understanding the iterator protocol is crucial for writing efficient Python code.
"""

from typing import Iterator, Iterable, Any
import itertools

def iterator_protocol_basics():
    """迭代器协议基础 / Iterator Protocol Basics"""
    print("=== 迭代器协议基础 Iterator Protocol Basics ===")
    
    print("1. 迭代器协议:")
    print("   - 可迭代对象 (Iterable): 实现 __iter__() 方法")
    print("   - 迭代器 (Iterator): 实现 __iter__() 和 __next__() 方法")
    print("   - __iter__() 返回迭代器对象")
    print("   - __next__() 返回下一个元素，没有元素时抛出 StopIteration")
    
    print("\n2. 内置可迭代对象:")
    
    # 列表迭代 / List iteration
    my_list = [1, 2, 3, 4, 5]
    print(f"列表: {my_list}")
    print(f"是否可迭代: {hasattr(my_list, '__iter__')}")
    
    # 获取迭代器 / Get iterator
    list_iterator = iter(my_list)
    print(f"迭代器类型: {type(list_iterator)}")
    print(f"是否是迭代器: {hasattr(list_iterator, '__next__')}")
    
    # 手动迭代 / Manual iteration
    print("手动迭代:")
    try:
        while True:
            item = next(list_iterator)
            print(f"  {item}")
    except StopIteration:
        print("  迭代结束")
    
    print("\n3. 字符串迭代:")
    text = "Hello"
    print(f"字符串: '{text}'")
    for char in text:
        print(f"  字符: '{char}'")
    
    print("\n4. 字典迭代:")
    my_dict = {"a": 1, "b": 2, "c": 3}
    print(f"字典: {my_dict}")
    
    print("迭代键:")
    for key in my_dict:
        print(f"  键: {key}")
    
    print("迭代值:")
    for value in my_dict.values():
        print(f"  值: {value}")
    
    print("迭代键值对:")
    for key, value in my_dict.items():
        print(f"  {key}: {value}")

def custom_iterator_class():
    """自定义迭代器类 / Custom Iterator Class"""
    print("\n=== 自定义迭代器类 Custom Iterator Class ===")
    
    class NumberSequence:
        """数字序列迭代器"""
        
        def __init__(self, start, end, step=1):
            self.start = start
            self.end = end
            self.step = step
            self.current = start
        
        def __iter__(self):
            """返回迭代器对象 (自己)"""
            return self
        
        def __next__(self):
            """返回下一个元素"""
            if (self.step > 0 and self.current >= self.end) or \
               (self.step < 0 and self.current <= self.end):
                raise StopIteration
            
            result = self.current
            self.current += self.step
            return result
        
        def __repr__(self):
            return f"NumberSequence({self.start}, {self.end}, {self.step})"
    
    print("1. 基本数字序列:")
    seq1 = NumberSequence(1, 6)
    print(f"序列: {seq1}")
    print("迭代结果:")
    for num in seq1:
        print(f"  {num}")
    
    print("\n2. 带步长的序列:")
    seq2 = NumberSequence(0, 10, 2)
    print(f"序列: {seq2}")
    print(f"迭代结果: {list(seq2)}")
    
    print("\n3. 反向序列:")
    seq3 = NumberSequence(10, 0, -2)
    print(f"序列: {seq3}")
    print(f"迭代结果: {list(seq3)}")
    
    print("\n4. 迭代器的一次性特性:")
    seq4 = NumberSequence(1, 4)
    print(f"第一次迭代: {list(seq4)}")
    print(f"第二次迭代: {list(seq4)}")  # 空列表，因为迭代器已耗尽
    
    # 重新创建迭代器 / Recreate iterator
    seq4_new = NumberSequence(1, 4)
    print(f"重新创建后迭代: {list(seq4_new)}")

def iterable_vs_iterator():
    """可迭代对象 vs 迭代器 / Iterable vs Iterator"""
    print("\n=== 可迭代对象 vs 迭代器 Iterable vs Iterator ===")
    
    class CountDown:
        """倒计时 - 可迭代对象 (不是迭代器)"""
        
        def __init__(self, start):
            self.start = start
        
        def __iter__(self):
            """返回新的迭代器实例"""
            return CountDownIterator(self.start)
        
        def __repr__(self):
            return f"CountDown({self.start})"
    
    class CountDownIterator:
        """倒计时迭代器"""
        
        def __init__(self, start):
            self.current = start
        
        def __iter__(self):
            return self
        
        def __next__(self):
            if self.current <= 0:
                raise StopIteration
            
            result = self.current
            self.current -= 1
            return result
    
    print("1. 可迭代对象可以多次迭代:")
    countdown = CountDown(3)
    
    print(f"第一次迭代: {list(countdown)}")
    print(f"第二次迭代: {list(countdown)}")
    print(f"第三次迭代: {list(countdown)}")
    
    print("\n2. 手动获取迭代器:")
    iterator1 = iter(countdown)
    iterator2 = iter(countdown)
    
    print(f"迭代器1: {list(iterator1)}")
    print(f"迭代器2: {list(iterator2)}")
    print(f"迭代器1和2是不同对象: {iterator1 is not iterator2}")

def fibonacci_iterator():
    """斐波那契迭代器 / Fibonacci Iterator"""
    print("\n=== 斐波那契迭代器 Fibonacci Iterator ===")
    
    class Fibonacci:
        """斐波那契数列迭代器"""
        
        def __init__(self, max_count=None):
            self.max_count = max_count
            self.count = 0
            self.current = 0
            self.next_val = 1
        
        def __iter__(self):
            return self
        
        def __next__(self):
            if self.max_count is not None and self.count >= self.max_count:
                raise StopIteration
            
            result = self.current
            self.current, self.next_val = self.next_val, self.current + self.next_val
            self.count += 1
            return result
        
        def __repr__(self):
            return f"Fibonacci(max_count={self.max_count})"
    
    print("1. 有限斐波那契数列:")
    fib = Fibonacci(10)
    print(f"前10个斐波那契数: {list(fib)}")
    
    print("\n2. 使用for循环:")
    fib2 = Fibonacci(8)
    print("斐波那契数列:")
    for i, num in enumerate(fib2):
        print(f"  F({i}) = {num}")
    
    print("\n3. 无限斐波那契数列 (小心使用):")
    infinite_fib = Fibonacci()  # 无限序列
    
    # 只取前几个 / Take only first few
    result = []
    for num in infinite_fib:
        if len(result) >= 5:
            break
        result.append(num)
    
    print(f"无限序列的前5个: {result}")

def file_line_iterator():
    """文件行迭代器 / File Line Iterator"""
    print("\n=== 文件行迭代器 File Line Iterator ===")
    
    # 创建测试文件 / Create test file
    test_content = """第一行内容
第二行内容
第三行内容
第四行内容
第五行内容"""
    
    with open("test_file.txt", "w", encoding="utf-8") as f:
        f.write(test_content)
    
    class FileLineIterator:
        """文件行迭代器"""
        
        def __init__(self, filename, encoding="utf-8"):
            self.filename = filename
            self.encoding = encoding
            self.file = None
        
        def __iter__(self):
            self.file = open(self.filename, 'r', encoding=self.encoding)
            return self
        
        def __next__(self):
            if self.file is None:
                raise StopIteration
            
            line = self.file.readline()
            if not line:
                self.file.close()
                raise StopIteration
            
            return line.strip()
        
        def __del__(self):
            """析构函数 - 确保文件被关闭"""
            if self.file and not self.file.closed:
                self.file.close()
    
    print("1. 自定义文件迭代器:")
    file_iter = FileLineIterator("test_file.txt")
    
    for line_num, line in enumerate(file_iter, 1):
        print(f"  行{line_num}: {line}")
    
    print("\n2. 与内置文件迭代器比较:")
    print("内置方式:")
    with open("test_file.txt", "r", encoding="utf-8") as f:
        for line_num, line in enumerate(f, 1):
            print(f"  行{line_num}: {line.strip()}")
    
    # 清理测试文件 / Clean up test file
    import os
    os.remove("test_file.txt")

def iterator_tools_examples():
    """迭代器工具示例 / Iterator Tools Examples"""
    print("\n=== 迭代器工具示例 Iterator Tools Examples ===")
    
    print("1. itertools.count() - 无限计数:")
    counter = itertools.count(start=10, step=2)
    print("前5个偶数 (从10开始):")
    for i, num in enumerate(counter):
        if i >= 5:
            break
        print(f"  {num}")
    
    print("\n2. itertools.cycle() - 循环迭代:")
    colors = ['red', 'green', 'blue']
    color_cycle = itertools.cycle(colors)
    
    print("循环颜色 (前8个):")
    for i, color in enumerate(color_cycle):
        if i >= 8:
            break
        print(f"  {i}: {color}")
    
    print("\n3. itertools.chain() - 链接迭代器:")
    list1 = [1, 2, 3]
    list2 = ['a', 'b', 'c']
    list3 = [10, 20, 30]
    
    chained = itertools.chain(list1, list2, list3)
    print(f"链接结果: {list(chained)}")
    
    print("\n4. itertools.islice() - 切片迭代器:")
    numbers = itertools.count(1)  # 无限数列
    first_10_evens = itertools.islice(
        (x for x in numbers if x % 2 == 0), 10
    )
    print(f"前10个偶数: {list(first_10_evens)}")
    
    print("\n5. itertools.takewhile() - 条件取值:")
    numbers = [1, 3, 5, 7, 8, 9, 11, 13]
    odd_numbers = itertools.takewhile(lambda x: x % 2 == 1, numbers)
    print(f"连续的奇数: {list(odd_numbers)}")
    
    print("\n6. itertools.groupby() - 分组:")
    data = [1, 1, 2, 2, 2, 3, 1, 1]
    grouped = itertools.groupby(data)
    
    print("分组结果:")
    for key, group in grouped:
        print(f"  {key}: {list(group)}")

def custom_range_iterator():
    """自定义范围迭代器 / Custom Range Iterator"""
    print("\n=== 自定义范围迭代器 Custom Range Iterator ===")
    
    class MyRange:
        """自定义范围类 - 类似内置range"""
        
        def __init__(self, *args):
            if len(args) == 1:
                self.start, self.stop, self.step = 0, args[0], 1
            elif len(args) == 2:
                self.start, self.stop, self.step = args[0], args[1], 1
            elif len(args) == 3:
                self.start, self.stop, self.step = args
            else:
                raise TypeError("MyRange expected 1 to 3 arguments")
            
            if self.step == 0:
                raise ValueError("step argument must not be zero")
        
        def __iter__(self):
            return MyRangeIterator(self.start, self.stop, self.step)
        
        def __len__(self):
            """计算范围长度"""
            if self.step > 0:
                return max(0, (self.stop - self.start + self.step - 1) // self.step)
            else:
                return max(0, (self.start - self.stop - self.step - 1) // (-self.step))
        
        def __repr__(self):
            if self.step == 1:
                return f"MyRange({self.start}, {self.stop})"
            else:
                return f"MyRange({self.start}, {self.stop}, {self.step})"
    
    class MyRangeIterator:
        """范围迭代器"""
        
        def __init__(self, start, stop, step):
            self.current = start
            self.stop = stop
            self.step = step
        
        def __iter__(self):
            return self
        
        def __next__(self):
            if (self.step > 0 and self.current >= self.stop) or \
               (self.step < 0 and self.current <= self.stop):
                raise StopIteration
            
            result = self.current
            self.current += self.step
            return result
    
    print("1. 基本用法:")
    my_range1 = MyRange(5)
    print(f"MyRange(5): {list(my_range1)}")
    
    my_range2 = MyRange(2, 8)
    print(f"MyRange(2, 8): {list(my_range2)}")
    
    my_range3 = MyRange(0, 10, 2)
    print(f"MyRange(0, 10, 2): {list(my_range3)}")
    
    print("\n2. 反向范围:")
    my_range4 = MyRange(10, 0, -2)
    print(f"MyRange(10, 0, -2): {list(my_range4)}")
    
    print("\n3. 长度计算:")
    print(f"len(MyRange(10)): {len(MyRange(10))}")
    print(f"len(MyRange(5, 15, 2)): {len(MyRange(5, 15, 2))}")
    
    print("\n4. 多次迭代:")
    my_range5 = MyRange(3)
    print(f"第一次: {list(my_range5)}")
    print(f"第二次: {list(my_range5)}")  # 可以多次迭代

def iterator_performance():
    """迭代器性能 / Iterator Performance"""
    print("\n=== 迭代器性能 Iterator Performance ===")
    
    import time
    import sys
    
    print("1. 内存使用比较:")
    
    # 列表 - 立即创建所有元素 / List - creates all elements immediately
    large_list = list(range(1000000))
    list_size = sys.getsizeof(large_list)
    print(f"列表大小: {list_size:,} 字节")
    
    # 迭代器 - 按需生成元素 / Iterator - generates elements on demand
    large_range = range(1000000)
    range_size = sys.getsizeof(large_range)
    print(f"range对象大小: {range_size:,} 字节")
    
    print(f"内存节省: {(list_size - range_size) / list_size * 100:.1f}%")
    
    print("\n2. 创建时间比较:")
    
    # 测试列表创建时间 / Test list creation time
    start_time = time.time()
    test_list = list(range(1000000))
    list_time = time.time() - start_time
    
    # 测试range创建时间 / Test range creation time
    start_time = time.time()
    test_range = range(1000000)
    range_time = time.time() - start_time
    
    print(f"列表创建时间: {list_time:.6f} 秒")
    print(f"range创建时间: {range_time:.6f} 秒")
    
    if range_time > 0:
        print(f"range快了: {list_time / range_time:.0f} 倍")
    else:
        print("range创建几乎瞬间完成")
    
    print("\n3. 迭代性能比较:")
    
    # 迭代列表 / Iterate list
    start_time = time.time()
    sum_list = sum(test_list)
    list_iter_time = time.time() - start_time
    
    # 迭代range / Iterate range
    start_time = time.time()
    sum_range = sum(test_range)
    range_iter_time = time.time() - start_time
    
    print(f"列表迭代时间: {list_iter_time:.6f} 秒")
    print(f"range迭代时间: {range_iter_time:.6f} 秒")
    print(f"结果相同: {sum_list == sum_range}")

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python迭代器学习示例")
    print("Python Iterator Learning Examples")
    print("=" * 50)
    
    iterator_protocol_basics()
    custom_iterator_class()
    iterable_vs_iterator()
    fibonacci_iterator()
    file_line_iterator()
    iterator_tools_examples()
    custom_range_iterator()
    iterator_performance()
    
    print("\n" + "=" * 50)
    print("迭代器学习完成！Iterator learning completed!")
