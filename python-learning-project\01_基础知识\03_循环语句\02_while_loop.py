#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
while循环 (While Loop) - Python条件循环
While Loop - Python Conditional Loop

while循环在给定条件为真时重复执行代码块。
While loops repeatedly execute a code block as long as a given condition is true.
"""

def basic_while_loops():
    """基本while循环 / Basic While Loops"""
    print("=== 基本while循环 Basic While Loops ===")
    
    # 简单计数 / Simple counting
    print("从1数到5:")
    count = 1
    while count <= 5:
        print(f"计数: {count}")
        count += 1
    
    # 倒计时 / Countdown
    print("\n倒计时:")
    countdown = 5
    while countdown > 0:
        print(f"{countdown}...")
        countdown -= 1
    print("发射！")
    
    # 累加求和 / Accumulative sum
    print("\n1到10的累加:")
    total = 0
    num = 1
    while num <= 10:
        total += num
        num += 1
    print(f"1到10的和: {total}")

def while_with_conditions():
    """带复杂条件的while循环 / While Loops with Complex Conditions"""
    print("\n=== 复杂条件while循环 Complex Condition While Loops ===")
    
    # 多条件循环 / Multiple condition loop
    print("寻找第一个大于100的平方数:")
    num = 1
    while num * num <= 100:
        square = num * num
        print(f"{num}² = {square}")
        num += 1
    print(f"第一个大于100的平方数: {num}² = {num * num}")
    
    # 字符串处理 / String processing
    print("\n移除字符串末尾的空格:")
    text = "Hello World    "
    print(f"原字符串: '{text}'")
    
    while text and text[-1] == ' ':
        text = text[:-1]
    print(f"处理后: '{text}'")
    
    # 列表处理 / List processing
    print("\n从列表中移除所有零:")
    numbers = [1, 0, 2, 0, 3, 0, 4]
    print(f"原列表: {numbers}")
    
    while 0 in numbers:
        numbers.remove(0)
    print(f"移除零后: {numbers}")

def infinite_loops_and_break():
    """无限循环和break / Infinite Loops and Break"""
    print("\n=== 无限循环和break Infinite Loops and Break ===")
    
    # 用户输入循环 / User input loop
    print("简单计算器(输入'quit'退出):")
    
    # 模拟用户输入 / Simulate user input
    inputs = ["10 + 5", "20 - 8", "6 * 7", "quit"]
    input_index = 0
    
    while True:
        if input_index >= len(inputs):
            break
        
        user_input = inputs[input_index]
        input_index += 1
        
        print(f"输入: {user_input}")
        
        if user_input.lower() == 'quit':
            print("退出计算器")
            break
        
        try:
            result = eval(user_input)  # 注意：实际应用中不建议使用eval
            print(f"结果: {result}")
        except:
            print("无效的表达式")
    
    # 查找特定元素 / Find specific element
    print("\n在列表中查找目标值:")
    numbers = [3, 7, 1, 9, 4, 6, 2, 8, 5]
    target = 6
    index = 0
    
    while index < len(numbers):
        if numbers[index] == target:
            print(f"找到目标值 {target} 在索引 {index}")
            break
        index += 1
    else:
        print(f"未找到目标值 {target}")

def while_with_continue():
    """while循环中的continue / Continue in While Loops"""
    print("\n=== while循环中的continue Continue in While Loops ===")
    
    # 跳过特定条件 / Skip specific conditions
    print("打印1到10中的奇数:")
    num = 0
    while num < 10:
        num += 1
        if num % 2 == 0:
            continue  # 跳过偶数
        print(f"奇数: {num}")
    
    # 数据过滤 / Data filtering
    print("\n处理数据列表(跳过负数和零):")
    data = [5, -2, 0, 8, -1, 3, 0, 7]
    index = 0
    positive_sum = 0
    
    while index < len(data):
        value = data[index]
        index += 1
        
        if value <= 0:
            print(f"跳过: {value}")
            continue
        
        positive_sum += value
        print(f"累加: {value}, 当前和: {positive_sum}")
    
    print(f"正数总和: {positive_sum}")

def while_else_clause():
    """while-else子句 / While-Else Clause"""
    print("\n=== while-else子句 While-Else Clause ===")
    
    # 正常结束的while循环 / Normally completed while loop
    print("查找质数:")
    def is_prime(n):
        if n < 2:
            return False
        
        divisor = 2
        while divisor * divisor <= n:
            if n % divisor == 0:
                return False
            divisor += 1
        else:
            # while循环正常结束(没有break)
            return True
    
    test_numbers = [17, 18, 19, 20]
    for num in test_numbers:
        if is_prime(num):
            print(f"{num} 是质数")
        else:
            print(f"{num} 不是质数")
    
    # 带break的while循环 / While loop with break
    print("\n密码验证(最多3次机会):")
    correct_password = "123456"
    attempts = 0
    max_attempts = 3
    
    # 模拟用户输入 / Simulate user input
    password_attempts = ["wrong1", "wrong2", "123456"]
    
    while attempts < max_attempts:
        password = password_attempts[attempts] if attempts < len(password_attempts) else "wrong"
        attempts += 1
        
        print(f"第{attempts}次尝试: {password}")
        
        if password == correct_password:
            print("密码正确，登录成功！")
            break
    else:
        # while循环正常结束(没有break)
        print("密码错误次数过多，账户被锁定！")

def practical_examples():
    """实际应用示例 / Practical Examples"""
    print("\n=== 实际应用示例 Practical Examples ===")
    
    # 1. 数字猜测游戏 / Number guessing game
    print("1. 数字猜测游戏模拟:")
    import random
    
    target = random.randint(1, 100)
    guesses = [50, 75, 88, 82, 85]  # 模拟猜测
    guess_count = 0
    
    print(f"目标数字: {target} (实际游戏中不显示)")
    
    while guess_count < len(guesses):
        guess = guesses[guess_count]
        guess_count += 1
        
        print(f"第{guess_count}次猜测: {guess}")
        
        if guess == target:
            print(f"恭喜！你在{guess_count}次内猜中了！")
            break
        elif guess < target:
            print("太小了！")
        else:
            print("太大了！")
    
    # 2. 银行账户模拟 / Bank account simulation
    print("\n2. 银行账户操作:")
    balance = 1000.0
    transactions = [
        ("deposit", 500),
        ("withdraw", 200),
        ("withdraw", 1500),  # 余额不足
        ("deposit", 300),
        ("check", 0)
    ]
    
    transaction_index = 0
    while transaction_index < len(transactions):
        action, amount = transactions[transaction_index]
        transaction_index += 1
        
        print(f"\n操作: {action}, 金额: {amount}")
        
        if action == "deposit":
            balance += amount
            print(f"存款成功，当前余额: {balance}")
        elif action == "withdraw":
            if balance >= amount:
                balance -= amount
                print(f"取款成功，当前余额: {balance}")
            else:
                print(f"余额不足，当前余额: {balance}")
        elif action == "check":
            print(f"当前余额: {balance}")
    
    # 3. 数据处理流水线 / Data processing pipeline
    print("\n3. 数据处理流水线:")
    raw_data = ["  hello  ", "WORLD", "123", "", "  Python  ", "programming"]
    processed_data = []
    
    index = 0
    while index < len(raw_data):
        item = raw_data[index]
        index += 1
        
        # 跳过空字符串 / Skip empty strings
        if not item.strip():
            print(f"跳过空字符串")
            continue
        
        # 数据清理和转换 / Data cleaning and transformation
        cleaned = item.strip().lower()
        
        # 跳过纯数字 / Skip pure numbers
        if cleaned.isdigit():
            print(f"跳过数字: {cleaned}")
            continue
        
        processed_data.append(cleaned)
        print(f"处理: '{item}' -> '{cleaned}'")
    
    print(f"处理结果: {processed_data}")

def performance_comparison():
    """性能比较 / Performance Comparison"""
    print("\n=== while vs for 性能比较 While vs For Performance Comparison ===")
    
    import time
    
    # while循环性能测试 / While loop performance test
    start_time = time.time()
    total = 0
    i = 0
    while i < 100000:
        total += i
        i += 1
    while_time = time.time() - start_time
    
    # for循环性能测试 / For loop performance test
    start_time = time.time()
    total = 0
    for i in range(100000):
        total += i
    for_time = time.time() - start_time
    
    print(f"while循环耗时: {while_time:.6f}秒")
    print(f"for循环耗时: {for_time:.6f}秒")
    print(f"for循环比while循环快 {while_time/for_time:.2f} 倍")
    
    print("\n使用建议:")
    print("- 已知迭代次数时使用for循环")
    print("- 基于条件判断时使用while循环")
    print("- for循环通常性能更好且代码更简洁")

def common_pitfalls():
    """常见陷阱 / Common Pitfalls"""
    print("\n=== 常见陷阱 Common Pitfalls ===")
    
    # 1. 无限循环 / Infinite loop
    print("1. 避免无限循环:")
    print("错误示例: while True: print('无限循环')  # 没有break条件")
    print("正确示例: 总是确保有退出条件")
    
    # 2. 条件永不改变 / Condition never changes
    print("\n2. 确保循环条件会改变:")
    print("错误示例:")
    print("x = 10")
    print("while x > 0:")
    print("    print(x)  # x永远不变，无限循环")
    
    print("正确示例:")
    x = 3
    while x > 0:
        print(f"x = {x}")
        x -= 1  # 确保条件会改变
    
    # 3. 修改正在迭代的列表 / Modifying list while iterating
    print("\n3. 避免在迭代时修改列表:")
    numbers = [1, 2, 3, 4, 5]
    print(f"原列表: {numbers}")
    
    # 错误方式(可能跳过元素)
    print("错误方式可能导致问题")
    
    # 正确方式：从后往前删除或创建新列表
    numbers_copy = numbers.copy()
    index = len(numbers_copy) - 1
    while index >= 0:
        if numbers_copy[index] % 2 == 0:
            del numbers_copy[index]
        index -= 1
    
    print(f"删除偶数后: {numbers_copy}")

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python while循环学习示例")
    print("Python While Loop Learning Examples")
    print("=" * 50)
    
    basic_while_loops()
    while_with_conditions()
    infinite_loops_and_break()
    while_with_continue()
    while_else_clause()
    practical_examples()
    performance_comparison()
    common_pitfalls()
    
    print("\n" + "=" * 50)
    print("while循环学习完成！While loop learning completed!")
