#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python与其他语言结合 - 实际代码示例
Python Integration with Other Languages - Practical Code Examples

本文件包含可以直接运行的Python与其他语言集成的示例代码。
This file contains directly runnable example code for Python integration with other languages.
"""

import ctypes
import subprocess
import json
import time
import sys
import os

def ctypes_math_example():
    """使用ctypes调用C数学库 / Using ctypes to call C math library"""
    print("=== ctypes数学库示例 ===")
    
    try:
        # 根据操作系统加载数学库 / Load math library based on OS
        if sys.platform.startswith('win'):
            # Windows
            libm = ctypes.CDLL('msvcrt.dll')
        elif sys.platform.startswith('darwin'):
            # macOS
            libm = ctypes.CDLL('libm.dylib')
        else:
            # Linux
            libm = ctypes.CDLL('libm.so.6')
        
        # 设置函数签名 / Set function signatures
        libm.sin.argtypes = [ctypes.c_double]
        libm.sin.restype = ctypes.c_double
        
        libm.cos.argtypes = [ctypes.c_double]
        libm.cos.restype = ctypes.c_double
        
        libm.sqrt.argtypes = [ctypes.c_double]
        libm.sqrt.restype = ctypes.c_double
        
        # 测试数学函数 / Test math functions
        import math
        
        test_values = [0, math.pi/6, math.pi/4, math.pi/3, math.pi/2]
        
        print("角度\t\tC sin()\t\tPython sin()\t差异")
        print("-" * 60)
        
        for angle in test_values:
            c_sin = libm.sin(angle)
            py_sin = math.sin(angle)
            diff = abs(c_sin - py_sin)
            
            print(f"{angle:.4f}\t\t{c_sin:.6f}\t{py_sin:.6f}\t{diff:.2e}")
        
        # 性能比较 / Performance comparison
        print("\n性能比较 (计算sin(x) 100万次):")
        
        # C库性能测试 / C library performance test
        start_time = time.time()
        for _ in range(1000000):
            libm.sin(1.0)
        c_time = time.time() - start_time
        
        # Python性能测试 / Python performance test
        start_time = time.time()
        for _ in range(1000000):
            math.sin(1.0)
        py_time = time.time() - start_time
        
        print(f"C库耗时: {c_time:.4f} 秒")
        print(f"Python耗时: {py_time:.4f} 秒")
        print(f"性能比: {py_time/c_time:.2f}x")
        
    except OSError as e:
        print(f"无法加载数学库: {e}")
        print("这在某些系统上是正常的，因为库的位置可能不同")

def subprocess_integration_example():
    """使用subprocess与其他语言程序交互 / Using subprocess to interact with other language programs"""
    print("\n=== subprocess集成示例 ===")
    
    # 1. 调用系统命令 / Call system commands
    print("1. 调用系统命令:")
    
    try:
        # 获取当前目录文件列表 / Get current directory file list
        if sys.platform.startswith('win'):
            result = subprocess.run(['dir'], shell=True, capture_output=True, text=True)
        else:
            result = subprocess.run(['ls', '-la'], capture_output=True, text=True)
        
        print("命令输出 (前5行):")
        lines = result.stdout.split('\n')[:5]
        for line in lines:
            print(f"  {line}")
            
    except Exception as e:
        print(f"执行命令失败: {e}")
    
    # 2. 与Node.js交互 / Interact with Node.js
    print("\n2. 与Node.js交互:")
    
    # 创建临时的Node.js脚本 / Create temporary Node.js script
    node_script = '''
const fs = require('fs');

// 接收Python传递的数据
const input = process.argv[2];
const data = JSON.parse(input);

// 处理数据
const result = {
    original: data,
    processed: data.numbers.map(n => n * n),
    sum: data.numbers.reduce((a, b) => a + b, 0),
    timestamp: new Date().toISOString()
};

// 返回结果给Python
console.log(JSON.stringify(result));
'''
    
    # 写入临时文件 / Write to temporary file
    with open('temp_node_script.js', 'w') as f:
        f.write(node_script)
    
    try:
        # 准备数据 / Prepare data
        python_data = {
            "numbers": [1, 2, 3, 4, 5],
            "message": "Hello from Python"
        }
        
        # 调用Node.js脚本 / Call Node.js script
        result = subprocess.run([
            'node', 'temp_node_script.js', 
            json.dumps(python_data)
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            # 解析Node.js返回的结果 / Parse result from Node.js
            node_result = json.loads(result.stdout.strip())
            
            print("发送给Node.js的数据:", python_data)
            print("Node.js处理结果:")
            print(f"  原始数据: {node_result['original']}")
            print(f"  平方结果: {node_result['processed']}")
            print(f"  数字总和: {node_result['sum']}")
            print(f"  处理时间: {node_result['timestamp']}")
        else:
            print("Node.js脚本执行失败 (可能未安装Node.js)")
            print("错误信息:", result.stderr)
            
    except FileNotFoundError:
        print("未找到Node.js，请安装Node.js以运行此示例")
    except Exception as e:
        print(f"Node.js交互失败: {e}")
    finally:
        # 清理临时文件 / Clean up temporary file
        if os.path.exists('temp_node_script.js'):
            os.remove('temp_node_script.js')

def python_c_performance_comparison():
    """Python与C性能比较示例 / Python vs C performance comparison example"""
    print("\n=== Python与C性能比较 ===")
    
    def fibonacci_python(n):
        """Python版本的斐波那契函数"""
        if n <= 1:
            return n
        return fibonacci_python(n-1) + fibonacci_python(n-2)
    
    def fibonacci_iterative_python(n):
        """Python迭代版本的斐波那契函数"""
        if n <= 1:
            return n
        a, b = 0, 1
        for _ in range(2, n + 1):
            a, b = b, a + b
        return b
    
    # 创建C语言版本的斐波那契程序 / Create C version of Fibonacci program
    c_code = '''
#include <stdio.h>
#include <stdlib.h>
#include <time.h>

long long fibonacci(int n) {
    if (n <= 1) return n;
    
    long long a = 0, b = 1, temp;
    for (int i = 2; i <= n; i++) {
        temp = a + b;
        a = b;
        b = temp;
    }
    return b;
}

int main(int argc, char *argv[]) {
    if (argc != 2) {
        printf("Usage: %s <number>\\n", argv[0]);
        return 1;
    }
    
    int n = atoi(argv[1]);
    clock_t start = clock();
    
    long long result = fibonacci(n);
    
    clock_t end = clock();
    double time_spent = ((double)(end - start)) / CLOCKS_PER_SEC;
    
    printf("%lld\\n", result);
    printf("Time: %f seconds\\n", time_spent);
    
    return 0;
}
'''
    
    # 写入C代码文件 / Write C code file
    with open('fibonacci.c', 'w') as f:
        f.write(c_code)
    
    try:
        # 编译C程序 / Compile C program
        compile_result = subprocess.run([
            'gcc', '-o', 'fibonacci', 'fibonacci.c'
        ], capture_output=True, text=True)
        
        if compile_result.returncode == 0:
            print("C程序编译成功")
            
            # 测试不同的n值 / Test different n values
            test_values = [30, 35, 40]
            
            for n in test_values:
                print(f"\n计算斐波那契数列第{n}项:")
                
                # Python迭代版本性能测试 / Python iterative version performance test
                start_time = time.time()
                py_result = fibonacci_iterative_python(n)
                py_time = time.time() - start_time
                
                # C版本性能测试 / C version performance test
                c_result = subprocess.run([
                    './fibonacci', str(n)
                ], capture_output=True, text=True)
                
                if c_result.returncode == 0:
                    c_output = c_result.stdout.strip().split('\n')
                    c_fib_result = int(c_output[0])
                    c_time_str = c_output[1].split(': ')[1].split(' ')[0]
                    c_time = float(c_time_str)
                    
                    print(f"  Python结果: {py_result}, 耗时: {py_time:.6f} 秒")
                    print(f"  C结果: {c_fib_result}, 耗时: {c_time:.6f} 秒")
                    print(f"  结果一致: {py_result == c_fib_result}")
                    
                    if c_time > 0:
                        speedup = py_time / c_time
                        print(f"  C语言加速比: {speedup:.2f}x")
                else:
                    print(f"  C程序执行失败: {c_result.stderr}")
        else:
            print("C程序编译失败 (可能未安装GCC编译器)")
            print("错误信息:", compile_result.stderr)
            
    except FileNotFoundError:
        print("未找到GCC编译器，请安装GCC以运行此示例")
    except Exception as e:
        print(f"C程序测试失败: {e}")
    finally:
        # 清理临时文件 / Clean up temporary files
        for filename in ['fibonacci.c', 'fibonacci', 'fibonacci.exe']:
            if os.path.exists(filename):
                os.remove(filename)

def json_data_exchange_example():
    """JSON数据交换示例 / JSON data exchange example"""
    print("\n=== JSON数据交换示例 ===")
    
    # 创建Python脚本与其他语言程序交换数据 / Create Python script to exchange data with other language programs
    
    # 1. 与Python子进程交换数据 / Exchange data with Python subprocess
    print("1. Python进程间数据交换:")
    
    worker_script = '''
import sys
import json
import math

# 读取输入数据
input_data = json.loads(sys.argv[1])

# 处理数据
result = {
    "input": input_data,
    "calculations": {
        "squares": [x**2 for x in input_data["numbers"]],
        "square_roots": [math.sqrt(x) for x in input_data["numbers"]],
        "sum": sum(input_data["numbers"]),
        "average": sum(input_data["numbers"]) / len(input_data["numbers"])
    },
    "metadata": {
        "processed_by": "Python worker",
        "item_count": len(input_data["numbers"])
    }
}

# 输出结果
print(json.dumps(result, indent=2))
'''
    
    # 写入工作脚本 / Write worker script
    with open('data_worker.py', 'w') as f:
        f.write(worker_script)
    
    try:
        # 准备数据 / Prepare data
        input_data = {
            "numbers": [1, 4, 9, 16, 25],
            "operation": "mathematical_processing"
        }
        
        # 调用工作进程 / Call worker process
        result = subprocess.run([
            sys.executable, 'data_worker.py',
            json.dumps(input_data)
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            # 解析结果 / Parse result
            worker_result = json.loads(result.stdout)
            
            print("输入数据:", input_data)
            print("处理结果:")
            print(f"  平方: {worker_result['calculations']['squares']}")
            print(f"  平方根: {worker_result['calculations']['square_roots']}")
            print(f"  总和: {worker_result['calculations']['sum']}")
            print(f"  平均值: {worker_result['calculations']['average']:.2f}")
            print(f"  处理者: {worker_result['metadata']['processed_by']}")
        else:
            print("工作进程执行失败:", result.stderr)
            
    except Exception as e:
        print(f"数据交换失败: {e}")
    finally:
        # 清理临时文件 / Clean up temporary file
        if os.path.exists('data_worker.py'):
            os.remove('data_worker.py')

def integration_best_practices():
    """集成最佳实践示例 / Integration best practices example"""
    print("\n=== 集成最佳实践 ===")
    
    practices = [
        {
            "实践": "错误处理",
            "说明": "始终处理子进程可能的错误和异常",
            "示例": "使用try-except包装subprocess调用"
        },
        {
            "实践": "数据验证",
            "说明": "验证从其他语言程序接收的数据",
            "示例": "检查JSON格式和必需字段"
        },
        {
            "实践": "资源清理",
            "说明": "及时清理临时文件和进程",
            "示例": "使用finally块或上下文管理器"
        },
        {
            "实践": "性能监控",
            "说明": "监控跨语言调用的性能开销",
            "示例": "测量调用时间和内存使用"
        },
        {
            "实践": "版本兼容",
            "说明": "确保不同语言版本的兼容性",
            "示例": "检查外部程序的版本和可用性"
        }
    ]
    
    for i, practice in enumerate(practices, 1):
        print(f"{i}. {practice['实践']}")
        print(f"   说明: {practice['说明']}")
        print(f"   示例: {practice['示例']}")
        print()
    
    print("选择集成方案的考虑因素:")
    factors = [
        "性能要求 - CPU密集型任务考虑C/C++",
        "开发效率 - 简单任务优先使用Python",
        "维护成本 - 考虑团队技能和长期维护",
        "生态系统 - 利用现有库和工具",
        "部署复杂度 - 考虑依赖和环境配置"
    ]
    
    for factor in factors:
        print(f"  • {factor}")

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python与其他语言集成实例")
    print("Python Integration with Other Languages Examples")
    print("=" * 60)
    
    # 运行所有示例 / Run all examples
    ctypes_math_example()
    subprocess_integration_example()
    python_c_performance_comparison()
    json_data_exchange_example()
    integration_best_practices()
    
    print("\n" + "=" * 60)
    print("语言集成学习完成！Language integration learning completed!")
    print("\n注意事项:")
    print("- 某些示例需要安装相应的编译器或运行时环境")
    print("- 在生产环境中使用时，请添加更完善的错误处理")
    print("- 根据实际需求选择合适的集成方案")
