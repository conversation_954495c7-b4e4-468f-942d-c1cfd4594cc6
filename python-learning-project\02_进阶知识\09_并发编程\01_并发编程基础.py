#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并发编程 (Concurrent Programming) - Python并发编程基础
Concurrent Programming - Python Concurrent Programming Basics

并发编程允许程序同时执行多个任务，提高程序的效率和响应性。
Concurrent programming allows programs to execute multiple tasks simultaneously, improving efficiency and responsiveness.
"""

import threading
import multiprocessing
import asyncio
import time
import concurrent.futures
from typing import List, Any

def threading_basics():
    """线程基础 / Threading Basics"""
    print("=== 线程基础 Threading Basics ===")
    
    def worker_function(name: str, duration: int):
        """工作函数"""
        print(f"线程 {name} 开始工作")
        for i in range(duration):
            print(f"线程 {name} 工作中... {i+1}/{duration}")
            time.sleep(0.5)
        print(f"线程 {name} 完成工作")
    
    print("1. 创建和启动线程:")
    
    # 创建线程 / Create threads
    thread1 = threading.Thread(target=worker_function, args=("A", 3))
    thread2 = threading.Thread(target=worker_function, args=("B", 2))
    
    # 启动线程 / Start threads
    start_time = time.time()
    thread1.start()
    thread2.start()
    
    # 等待线程完成 / Wait for threads to complete
    thread1.join()
    thread2.join()
    
    end_time = time.time()
    print(f"总耗时: {end_time - start_time:.2f} 秒")
    
    print("\n2. 使用Thread类:")
    
    class WorkerThread(threading.Thread):
        """自定义线程类"""
        
        def __init__(self, name: str, task_count: int):
            super().__init__()
            self.name = name
            self.task_count = task_count
            self.result = []
        
        def run(self):
            """线程执行的主要方法"""
            print(f"自定义线程 {self.name} 开始")
            for i in range(self.task_count):
                result = i ** 2
                self.result.append(result)
                print(f"线程 {self.name}: {i}² = {result}")
                time.sleep(0.2)
            print(f"自定义线程 {self.name} 完成")
    
    # 创建并启动自定义线程 / Create and start custom threads
    worker1 = WorkerThread("Worker1", 3)
    worker2 = WorkerThread("Worker2", 4)
    
    worker1.start()
    worker2.start()
    
    worker1.join()
    worker2.join()
    
    print(f"Worker1 结果: {worker1.result}")
    print(f"Worker2 结果: {worker2.result}")

def thread_synchronization():
    """线程同步 / Thread Synchronization"""
    print("\n=== 线程同步 Thread Synchronization ===")
    
    print("1. 不使用锁的问题:")
    
    # 共享资源 / Shared resource
    shared_counter = 0
    
    def increment_without_lock():
        """不使用锁的递增函数"""
        global shared_counter
        for _ in range(100000):
            shared_counter += 1
    
    # 重置计数器 / Reset counter
    shared_counter = 0
    
    # 创建多个线程同时修改共享资源 / Create multiple threads modifying shared resource
    threads = []
    for i in range(5):
        thread = threading.Thread(target=increment_without_lock)
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()
    
    print(f"不使用锁的结果 (期望500000): {shared_counter}")
    
    print("\n2. 使用锁保证线程安全:")
    
    # 创建锁 / Create lock
    counter_lock = threading.Lock()
    shared_counter = 0
    
    def increment_with_lock():
        """使用锁的递增函数"""
        global shared_counter
        for _ in range(100000):
            with counter_lock:  # 使用with语句自动获取和释放锁
                shared_counter += 1
    
    # 重置计数器 / Reset counter
    shared_counter = 0
    
    # 创建多个线程 / Create multiple threads
    threads = []
    for i in range(5):
        thread = threading.Thread(target=increment_with_lock)
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()
    
    print(f"使用锁的结果 (期望500000): {shared_counter}")
    
    print("\n3. 生产者-消费者模式:")
    
    import queue
    
    # 创建队列 / Create queue
    task_queue = queue.Queue(maxsize=5)
    
    def producer(name: str, item_count: int):
        """生产者函数"""
        for i in range(item_count):
            item = f"{name}-item-{i}"
            task_queue.put(item)
            print(f"生产者 {name} 生产: {item}")
            time.sleep(0.1)
        print(f"生产者 {name} 完成")
    
    def consumer(name: str):
        """消费者函数"""
        while True:
            try:
                item = task_queue.get(timeout=1)
                print(f"消费者 {name} 消费: {item}")
                time.sleep(0.2)
                task_queue.task_done()
            except queue.Empty:
                print(f"消费者 {name} 超时退出")
                break
    
    # 创建生产者和消费者线程 / Create producer and consumer threads
    producer_thread = threading.Thread(target=producer, args=("P1", 5))
    consumer_thread1 = threading.Thread(target=consumer, args=("C1",))
    consumer_thread2 = threading.Thread(target=consumer, args=("C2",))
    
    producer_thread.start()
    consumer_thread1.start()
    consumer_thread2.start()
    
    producer_thread.join()
    consumer_thread1.join()
    consumer_thread2.join()

def multiprocessing_basics():
    """多进程基础 / Multiprocessing Basics"""
    print("\n=== 多进程基础 Multiprocessing Basics ===")
    
    def cpu_intensive_task(n: int) -> int:
        """CPU密集型任务"""
        result = 0
        for i in range(n):
            result += i ** 2
        return result
    
    print("1. 单进程执行:")
    start_time = time.time()
    
    results = []
    for i in range(4):
        result = cpu_intensive_task(1000000)
        results.append(result)
    
    single_process_time = time.time() - start_time
    print(f"单进程结果: {results}")
    print(f"单进程耗时: {single_process_time:.2f} 秒")
    
    print("\n2. 多进程执行:")
    
    def worker_process(n: int, return_dict: dict, process_id: int):
        """工作进程函数"""
        result = cpu_intensive_task(n)
        return_dict[process_id] = result
        print(f"进程 {process_id} 完成，结果: {result}")
    
    start_time = time.time()
    
    # 使用Manager创建共享字典 / Use Manager to create shared dictionary
    manager = multiprocessing.Manager()
    return_dict = manager.dict()
    
    # 创建进程 / Create processes
    processes = []
    for i in range(4):
        process = multiprocessing.Process(
            target=worker_process, 
            args=(1000000, return_dict, i)
        )
        processes.append(process)
        process.start()
    
    # 等待所有进程完成 / Wait for all processes to complete
    for process in processes:
        process.join()
    
    multi_process_time = time.time() - start_time
    print(f"多进程结果: {dict(return_dict)}")
    print(f"多进程耗时: {multi_process_time:.2f} 秒")
    print(f"加速比: {single_process_time / multi_process_time:.2f}x")

def process_pool_example():
    """进程池示例 / Process Pool Example"""
    print("\n=== 进程池示例 Process Pool Example ===")
    
    def calculate_square(n: int) -> int:
        """计算平方"""
        print(f"进程 {multiprocessing.current_process().name} 计算 {n}²")
        time.sleep(0.1)  # 模拟计算时间
        return n ** 2
    
    print("1. 使用进程池:")
    
    numbers = list(range(1, 11))
    
    # 使用进程池 / Use process pool
    with multiprocessing.Pool(processes=4) as pool:
        start_time = time.time()
        results = pool.map(calculate_square, numbers)
        pool_time = time.time() - start_time
    
    print(f"输入: {numbers}")
    print(f"结果: {results}")
    print(f"进程池耗时: {pool_time:.2f} 秒")
    
    print("\n2. 异步进程池:")
    
    def async_callback(result):
        """异步回调函数"""
        print(f"异步结果: {result}")
    
    with multiprocessing.Pool(processes=2) as pool:
        # 异步提交任务 / Submit tasks asynchronously
        async_results = []
        for num in [5, 6, 7]:
            async_result = pool.apply_async(calculate_square, (num,), callback=async_callback)
            async_results.append(async_result)
        
        # 获取结果 / Get results
        final_results = [ar.get() for ar in async_results]
        print(f"异步最终结果: {final_results}")

def concurrent_futures_example():
    """concurrent.futures示例 / concurrent.futures Example"""
    print("\n=== concurrent.futures示例 concurrent.futures Example ===")
    
    def download_simulation(url: str) -> str:
        """模拟下载任务"""
        print(f"开始下载: {url}")
        time.sleep(1)  # 模拟网络延迟
        return f"Downloaded content from {url}"
    
    urls = [
        "http://example1.com",
        "http://example2.com", 
        "http://example3.com",
        "http://example4.com"
    ]
    
    print("1. 使用ThreadPoolExecutor:")
    
    start_time = time.time()
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        # 提交任务 / Submit tasks
        future_to_url = {executor.submit(download_simulation, url): url for url in urls}
        
        # 获取结果 / Get results
        for future in concurrent.futures.as_completed(future_to_url):
            url = future_to_url[future]
            try:
                result = future.result()
                print(f"完成: {url} -> {result}")
            except Exception as exc:
                print(f"错误: {url} -> {exc}")
    
    thread_time = time.time() - start_time
    print(f"线程池耗时: {thread_time:.2f} 秒")
    
    print("\n2. 使用ProcessPoolExecutor:")
    
    def cpu_task(n: int) -> int:
        """CPU密集型任务"""
        return sum(i ** 2 for i in range(n))
    
    numbers = [100000, 200000, 300000, 400000]
    
    start_time = time.time()
    
    with concurrent.futures.ProcessPoolExecutor(max_workers=2) as executor:
        # 使用map方法 / Use map method
        results = list(executor.map(cpu_task, numbers))
    
    process_time = time.time() - start_time
    print(f"输入: {numbers}")
    print(f"结果: {results}")
    print(f"进程池耗时: {process_time:.2f} 秒")

def asyncio_basics():
    """异步编程基础 / Asyncio Basics"""
    print("\n=== 异步编程基础 Asyncio Basics ===")
    
    async def async_task(name: str, duration: int):
        """异步任务"""
        print(f"异步任务 {name} 开始")
        await asyncio.sleep(duration)  # 异步等待
        print(f"异步任务 {name} 完成 (耗时 {duration} 秒)")
        return f"Task {name} result"
    
    async def main_async():
        """主异步函数"""
        print("1. 顺序执行异步任务:")
        start_time = time.time()
        
        result1 = await async_task("A", 1)
        result2 = await async_task("B", 2)
        
        sequential_time = time.time() - start_time
        print(f"顺序执行结果: {result1}, {result2}")
        print(f"顺序执行耗时: {sequential_time:.2f} 秒")
        
        print("\n2. 并发执行异步任务:")
        start_time = time.time()
        
        # 并发执行 / Concurrent execution
        results = await asyncio.gather(
            async_task("C", 1),
            async_task("D", 2),
            async_task("E", 1)
        )
        
        concurrent_time = time.time() - start_time
        print(f"并发执行结果: {results}")
        print(f"并发执行耗时: {concurrent_time:.2f} 秒")
        
        print("\n3. 使用asyncio.create_task:")
        start_time = time.time()
        
        # 创建任务 / Create tasks
        task1 = asyncio.create_task(async_task("F", 1))
        task2 = asyncio.create_task(async_task("G", 2))
        
        # 等待任务完成 / Wait for tasks to complete
        result1 = await task1
        result2 = await task2
        
        task_time = time.time() - start_time
        print(f"任务执行结果: {result1}, {result2}")
        print(f"任务执行耗时: {task_time:.2f} 秒")
    
    # 运行异步主函数 / Run async main function
    asyncio.run(main_async())

def performance_comparison():
    """性能比较 / Performance Comparison"""
    print("\n=== 性能比较 Performance Comparison ===")
    
    def io_bound_task():
        """I/O密集型任务模拟"""
        time.sleep(0.1)
        return "IO task completed"
    
    def cpu_bound_task():
        """CPU密集型任务模拟"""
        return sum(i ** 2 for i in range(100000))
    
    print("1. I/O密集型任务比较:")
    
    # 顺序执行 / Sequential execution
    start_time = time.time()
    for _ in range(10):
        io_bound_task()
    sequential_io_time = time.time() - start_time
    
    # 线程池执行 / Thread pool execution
    start_time = time.time()
    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(io_bound_task) for _ in range(10)]
        results = [future.result() for future in futures]
    thread_io_time = time.time() - start_time
    
    print(f"顺序执行I/O任务耗时: {sequential_io_time:.2f} 秒")
    print(f"线程池执行I/O任务耗时: {thread_io_time:.2f} 秒")
    print(f"I/O任务加速比: {sequential_io_time / thread_io_time:.2f}x")
    
    print("\n2. CPU密集型任务比较:")
    
    # 顺序执行 / Sequential execution
    start_time = time.time()
    for _ in range(4):
        cpu_bound_task()
    sequential_cpu_time = time.time() - start_time
    
    # 进程池执行 / Process pool execution
    start_time = time.time()
    with concurrent.futures.ProcessPoolExecutor(max_workers=4) as executor:
        futures = [executor.submit(cpu_bound_task) for _ in range(4)]
        results = [future.result() for future in futures]
    process_cpu_time = time.time() - start_time
    
    print(f"顺序执行CPU任务耗时: {sequential_cpu_time:.2f} 秒")
    print(f"进程池执行CPU任务耗时: {process_cpu_time:.2f} 秒")
    print(f"CPU任务加速比: {sequential_cpu_time / process_cpu_time:.2f}x")

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python并发编程学习示例")
    print("Python Concurrent Programming Learning Examples")
    print("=" * 60)
    
    threading_basics()
    thread_synchronization()
    
    # 注意：多进程示例在某些环境中可能需要特殊处理
    # Note: Multiprocessing examples may need special handling in some environments
    try:
        multiprocessing_basics()
        process_pool_example()
    except Exception as e:
        print(f"多进程示例跳过: {e}")
    
    concurrent_futures_example()
    asyncio_basics()
    performance_comparison()
    
    print("\n" + "=" * 60)
    print("并发编程学习完成！Concurrent programming learning completed!")
