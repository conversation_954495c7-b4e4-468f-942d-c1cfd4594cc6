# Python数据结构 / Python Data Structures

本模块包含Python中四种主要内置数据结构的详细学习示例和实践代码。

This module contains detailed learning examples and practical code for the four main built-in data structures in Python.

## 📚 学习内容 / Learning Content

### 1. 列表 (List) - `01_列表_list.py`
- **特点**: 有序、可变、允许重复元素
- **Features**: Ordered, mutable, allows duplicate elements
- **学习要点**:
  - 基本操作：创建、访问、修改
  - 列表方法：append, insert, remove, pop, sort等
  - 列表切片：[start:end:step]
  - 列表推导式：[expression for item in iterable if condition]
  - 实际应用：学生成绩管理、购物清单等

### 2. 元组 (Tuple) - `02_元组_tuple.py`
- **特点**: 有序、不可变、允许重复元素
- **Features**: Ordered, immutable, allows duplicate elements
- **学习要点**:
  - 元组创建和访问
  - 元组解包 (Tuple Unpacking)
  - 元组与列表的性能比较
  - 实际应用：坐标点、数据库记录、配置信息

### 3. 字典 (Dictionary) - `03_字典_dict.py`
- **特点**: 无序(Python 3.7+保持插入顺序)、可变、键值对存储
- **Features**: Unordered (Python 3.7+ maintains insertion order), mutable, key-value storage
- **学习要点**:
  - 字典创建和访问
  - 字典方法：keys(), values(), items(), get()等
  - 字典推导式：{key: value for item in iterable}
  - 嵌套字典处理
  - 实际应用：配置管理、缓存系统、数据统计

### 4. 集合 (Set) - `04_集合_set.py`
- **特点**: 无序、可变、元素唯一
- **Features**: Unordered, mutable, unique elements
- **学习要点**:
  - 集合创建和基本操作
  - 集合运算：并集、交集、差集、对称差集
  - 不可变集合 (frozenset)
  - 实际应用：数据去重、权限管理、标签系统

## 🚀 快速开始 / Quick Start

### 运行单个示例 / Run Individual Examples
```bash
# 运行列表示例
python 01_列表_list.py

# 运行元组示例
python 02_元组_tuple.py

# 运行字典示例
python 03_字典_dict.py

# 运行集合示例
python 04_集合_set.py
```

### 运行所有示例 / Run All Examples
```bash
# 在当前目录下运行所有Python文件
for file in *.py; do echo "=== Running $file ==="; python "$file"; echo; done
```

## 📊 数据结构对比 / Data Structure Comparison

| 特性 / Feature | 列表 List | 元组 Tuple | 字典 Dict | 集合 Set |
|----------------|-----------|------------|-----------|----------|
| 有序性 / Ordered | ✅ | ✅ | ✅ (3.7+) | ❌ |
| 可变性 / Mutable | ✅ | ❌ | ✅ | ✅ |
| 重复元素 / Duplicates | ✅ | ✅ | ❌ (键) | ❌ |
| 索引访问 / Index Access | ✅ | ✅ | ❌ | ❌ |
| 键值访问 / Key Access | ❌ | ❌ | ✅ | ❌ |
| 数学运算 / Math Operations | ❌ | ❌ | ❌ | ✅ |

## 🎯 学习建议 / Learning Tips

### 1. 循序渐进 / Progressive Learning
- 先掌握基本操作，再学习高级特性
- 每个数据结构都要动手实践
- 理解每种数据结构的适用场景

### 2. 性能考虑 / Performance Considerations
- **查找操作**: 字典和集合 O(1) > 列表和元组 O(n)
- **内存使用**: 元组 < 列表 < 字典 < 集合
- **创建速度**: 元组 > 列表 > 集合 > 字典

### 3. 选择指南 / Selection Guide
- **需要有序且可变**: 使用列表
- **需要有序且不可变**: 使用元组
- **需要键值对映射**: 使用字典
- **需要唯一元素或集合运算**: 使用集合

## 🔧 练习题 / Exercises

### 初级练习 / Beginner Exercises
1. 创建一个包含你最喜欢的5种水果的列表，并按字母顺序排序
2. 使用元组存储一个人的基本信息（姓名、年龄、城市）
3. 创建一个字典来存储3个学生的成绩信息
4. 从两个列表中找出共同元素（使用集合）

### 中级练习 / Intermediate Exercises
1. 实现一个简单的学生管理系统（使用字典和列表）
2. 编写函数统计文本中每个单词的出现次数
3. 使用集合运算分析两个月的用户活跃数据
4. 实现一个简单的购物车系统

### 高级练习 / Advanced Exercises
1. 设计一个多级菜单系统（嵌套字典）
2. 实现一个简单的推荐系统（基于集合相似度）
3. 编写数据结构性能测试程序
4. 创建一个配置文件解析器

## 📖 扩展阅读 / Further Reading

### 官方文档 / Official Documentation
- [Python Built-in Types](https://docs.python.org/3/library/stdtypes.html)
- [Python Data Structures Tutorial](https://docs.python.org/3/tutorial/datastructures.html)

### 相关概念 / Related Concepts
- 时间复杂度 (Time Complexity)
- 空间复杂度 (Space Complexity)
- 哈希表 (Hash Tables)
- 数据结构算法 (Data Structure Algorithms)

## 💡 实用技巧 / Practical Tips

### 1. 列表技巧
```python
# 列表去重并保持顺序
def unique_list(lst):
    seen = set()
    return [x for x in lst if not (x in seen or seen.add(x))]

# 列表分块
def chunk_list(lst, chunk_size):
    return [lst[i:i+chunk_size] for i in range(0, len(lst), chunk_size)]
```

### 2. 字典技巧
```python
# 字典合并 (Python 3.9+)
dict1 = {"a": 1, "b": 2}
dict2 = {"c": 3, "d": 4}
merged = dict1 | dict2

# 字典默认值
from collections import defaultdict
dd = defaultdict(list)
dd['key'].append('value')  # 自动创建空列表
```

### 3. 集合技巧
```python
# 快速去重
unique_items = list(set(items))

# 检查列表是否有重复元素
has_duplicates = len(items) != len(set(items))
```

---

**下一步**: 完成数据结构学习后，继续学习 [条件语句](../02_条件语句/README.md)

**Next Step**: After completing data structures, continue with [Conditional Statements](../02_条件语句/README.md)
