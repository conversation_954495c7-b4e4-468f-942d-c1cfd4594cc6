# Python与其他语言结合
# Python Integration with Other Languages

## 概述 / Overview

Python作为一种"胶水语言"，具有出色的与其他编程语言集成的能力。这种集成能力使得Python可以利用其他语言的性能优势，同时保持Python的易用性。

Python, as a "glue language," has excellent capabilities for integrating with other programming languages. This integration ability allows Python to leverage the performance advantages of other languages while maintaining Python's ease of use.

## Python与C/C++集成 / Python-C/C++ Integration

### 1. 使用ctypes调用C库 / Using ctypes to Call C Libraries

ctypes是Python标准库中用于调用动态链接库的模块。

```python
import ctypes
import os

def ctypes_example():
    """ctypes使用示例"""
    
    print("=== ctypes示例 ===")
    
    # 1. 调用C标准库函数
    print("1. 调用C标准库函数:")
    
    # 加载C标准库
    if os.name == 'nt':  # Windows
        libc = ctypes.CDLL('msvcrt.dll')
    else:  # Unix/Linux
        libc = ctypes.CDLL('libc.so.6')
    
    # 调用printf函数
    printf = libc.printf
    printf.argtypes = [ctypes.c_char_p]
    printf.restype = ctypes.c_int
    
    # 注意：在某些系统上printf可能不会直接输出到Python控制台
    result = printf(b"Hello from C printf!\n")
    print(f"printf返回值: {result}")
    
    # 2. 数学函数调用
    print("\n2. 调用数学函数:")
    
    # 加载数学库
    if os.name == 'nt':
        libm = ctypes.CDLL('msvcrt.dll')
    else:
        libm = ctypes.CDLL('libm.so.6')
    
    # 调用sin函数
    sin = libm.sin
    sin.argtypes = [ctypes.c_double]
    sin.restype = ctypes.c_double
    
    import math
    angle = math.pi / 4
    c_result = sin(angle)
    py_result = math.sin(angle)
    
    print(f"C sin({angle}): {c_result}")
    print(f"Python sin({angle}): {py_result}")
    print(f"结果相等: {abs(c_result - py_result) < 1e-10}")

# ctypes_example()
```

### 2. 编写C扩展模块 / Writing C Extension Modules

```c
// 示例C扩展代码 (math_extension.c)
/*
#include <Python.h>

// 计算斐波那契数列的C函数
static PyObject* fibonacci_c(PyObject* self, PyObject* args) {
    int n;
    if (!PyArg_ParseTuple(args, "i", &n)) {
        return NULL;
    }
    
    if (n <= 0) {
        return PyLong_FromLong(0);
    } else if (n == 1) {
        return PyLong_FromLong(1);
    }
    
    long a = 0, b = 1, temp;
    for (int i = 2; i <= n; i++) {
        temp = a + b;
        a = b;
        b = temp;
    }
    
    return PyLong_FromLong(b);
}

// 快速排序的C实现
static PyObject* quicksort_c(PyObject* self, PyObject* args) {
    PyObject* list;
    if (!PyArg_ParseTuple(args, "O", &list)) {
        return NULL;
    }
    
    // 这里应该实现快速排序算法
    // 为简化示例，直接返回原列表
    Py_INCREF(list);
    return list;
}

// 方法定义
static PyMethodDef MathMethods[] = {
    {"fibonacci_c", fibonacci_c, METH_VARARGS, "Calculate Fibonacci number"},
    {"quicksort_c", quicksort_c, METH_VARARGS, "Quick sort implementation"},
    {NULL, NULL, 0, NULL}
};

// 模块定义
static struct PyModuleDef mathmodule = {
    PyModuleDef_HEAD_INIT,
    "math_extension",
    "Math extension module",
    -1,
    MathMethods
};

// 模块初始化函数
PyMODINIT_FUNC PyInit_math_extension(void) {
    return PyModule_Create(&mathmodule);
}
*/
```

对应的setup.py文件：

```python
# setup.py
from distutils.core import setup, Extension

def create_c_extension_setup():
    """创建C扩展的setup.py示例"""
    
    setup_code = '''
from distutils.core import setup, Extension

module = Extension('math_extension',
                   sources=['math_extension.c'])

setup(name='MathExtension',
      version='1.0',
      description='Math extension package',
      ext_modules=[module])
'''
    
    print("=== C扩展setup.py示例 ===")
    print(setup_code)
    print("\n编译命令: python setup.py build_ext --inplace")

create_c_extension_setup()
```

### 3. 使用Cython / Using Cython

Cython是Python的超集，可以编译为C代码以提高性能。

```python
# 示例Cython代码 (fibonacci.pyx)
cython_code = '''
# fibonacci.pyx
def fibonacci_cy(int n):
    """Cython版本的斐波那契函数"""
    if n <= 0:
        return 0
    elif n == 1:
        return 1
    
    cdef int a = 0
    cdef int b = 1
    cdef int temp
    cdef int i
    
    for i in range(2, n + 1):
        temp = a + b
        a = b
        b = temp
    
    return b

def matrix_multiply_cy(list A, list B):
    """Cython版本的矩阵乘法"""
    cdef int rows_A = len(A)
    cdef int cols_A = len(A[0])
    cdef int cols_B = len(B[0])
    
    # 创建结果矩阵
    cdef list result = [[0 for _ in range(cols_B)] for _ in range(rows_A)]
    
    cdef int i, j, k
    cdef double sum_val
    
    for i in range(rows_A):
        for j in range(cols_B):
            sum_val = 0
            for k in range(cols_A):
                sum_val += A[i][k] * B[k][j]
            result[i][j] = sum_val
    
    return result
'''

def demonstrate_cython():
    """演示Cython使用"""
    
    print("=== Cython示例 ===")
    print("Cython代码示例:")
    print(cython_code)
    
    print("\n对应的setup.py:")
    setup_cython = '''
from setuptools import setup
from Cython.Build import cythonize

setup(
    ext_modules = cythonize("fibonacci.pyx")
)
'''
    print(setup_cython)
    
    print("\n编译命令:")
    print("1. pip install cython")
    print("2. python setup.py build_ext --inplace")
    
    # 性能比较示例
    print("\n性能比较 (理论值):")
    print("Python版本斐波那契(n=35): ~2.5秒")
    print("Cython版本斐波那契(n=35): ~0.1秒")
    print("加速比: ~25x")

demonstrate_cython()
```

## Python与Java集成 / Python-Java Integration

### 1. 使用Jython / Using Jython

```python
def jython_example():
    """Jython使用示例"""
    
    print("=== Jython示例 ===")
    
    jython_code = '''
# Jython代码示例
from java.util import ArrayList, HashMap
from java.lang import String
import java.io.File as File

def use_java_collections():
    """使用Java集合类"""
    
    # 使用ArrayList
    java_list = ArrayList()
    java_list.add("Hello")
    java_list.add("World")
    java_list.add("from")
    java_list.add("Java")
    
    print("Java ArrayList:", java_list)
    
    # 使用HashMap
    java_map = HashMap()
    java_map.put("name", "Python")
    java_map.put("version", "3.9")
    java_map.put("platform", "JVM")
    
    print("Java HashMap:", java_map)
    
    # 使用Java String方法
    java_string = String("Hello World")
    print("Java String length:", java_string.length())
    print("Java String uppercase:", java_string.toUpperCase())
    
    # 文件操作
    file = File("example.txt")
    print("File exists:", file.exists())
    print("File path:", file.getAbsolutePath())

def java_swing_gui():
    """使用Java Swing创建GUI"""
    
    from javax.swing import JFrame, JButton, JLabel
    from java.awt import FlowLayout
    from java.awt.event import ActionListener
    
    class ButtonListener(ActionListener):
        def actionPerformed(self, event):
            print("Button clicked!")
    
    frame = JFrame("Jython GUI")
    frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE)
    frame.setLayout(FlowLayout())
    
    label = JLabel("Hello from Jython!")
    button = JButton("Click Me")
    button.addActionListener(ButtonListener())
    
    frame.add(label)
    frame.add(button)
    frame.setSize(300, 100)
    frame.setVisible(True)

# 运行示例
use_java_collections()
# java_swing_gui()  # 取消注释以显示GUI
'''
    
    print("Jython代码示例:")
    print(jython_code)
    
    print("\n安装和使用Jython:")
    print("1. 下载Jython: https://www.jython.org/")
    print("2. 运行: java -jar jython-installer.jar")
    print("3. 使用: jython script.py")

jython_example()
```

### 2. 使用JPype / Using JPype

```python
def jpype_example():
    """JPype使用示例"""
    
    print("=== JPype示例 ===")
    
    jpype_code = '''
import jpype
import jpype.imports
from jpype.types import *

def start_jvm_and_use_java():
    """启动JVM并使用Java类"""
    
    # 启动JVM
    jpype.startJVM(classpath=['path/to/your/classes'])
    
    try:
        # 导入Java类
        from java.util import ArrayList, HashMap
        from java.lang import String, System
        
        # 使用Java ArrayList
        java_list = ArrayList()
        java_list.add("Python")
        java_list.add("Java")
        java_list.add("Integration")
        
        print("Java ArrayList size:", java_list.size())
        for item in java_list:
            print("Item:", item)
        
        # 使用Java HashMap
        java_map = HashMap()
        java_map.put("language", "Python")
        java_map.put("framework", "JPype")
        
        print("Java HashMap:", java_map)
        
        # 调用Java系统方法
        print("Java version:", System.getProperty("java.version"))
        print("OS name:", System.getProperty("os.name"))
        
        # 创建自定义Java对象
        # 假设有一个自定义的Calculator类
        # Calculator = jpype.JClass("com.example.Calculator")
        # calc = Calculator()
        # result = calc.add(10, 20)
        # print("Calculation result:", result)
        
    finally:
        # 关闭JVM
        jpype.shutdownJVM()

# 运行示例
start_jvm_and_use_java()
'''
    
    print("JPype代码示例:")
    print(jpype_code)
    
    print("\n安装JPype:")
    print("pip install JPype1")
    
    print("\n注意事项:")
    print("- 需要安装Java JDK")
    print("- 需要设置JAVA_HOME环境变量")
    print("- JVM只能启动一次，关闭后不能重新启动")

jpype_example()
```

## Python与JavaScript集成 / Python-JavaScript Integration

### 1. 使用PyV8 / Using PyV8

```python
def pyv8_example():
    """PyV8使用示例"""
    
    print("=== PyV8示例 ===")
    
    pyv8_code = '''
import PyV8

def run_javascript_in_python():
    """在Python中运行JavaScript代码"""
    
    # 创建JavaScript上下文
    with PyV8.JSContext() as ctx:
        # 执行JavaScript代码
        result = ctx.eval("1 + 2 + 3")
        print("JavaScript计算结果:", result)
        
        # 执行复杂的JavaScript代码
        js_code = """
        function fibonacci(n) {
            if (n <= 1) return n;
            return fibonacci(n - 1) + fibonacci(n - 2);
        }
        
        function processArray(arr) {
            return arr.map(x => x * 2).filter(x => x > 10);
        }
        
        // 返回结果
        ({
            fib10: fibonacci(10),
            processed: processArray([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
        })
        """
        
        result = ctx.eval(js_code)
        print("斐波那契数列第10项:", result.fib10)
        print("处理后的数组:", result.processed)

def python_js_interaction():
    """Python和JavaScript交互"""
    
    class PythonObject:
        def __init__(self, name):
            self.name = name
        
        def greet(self, message):
            return f"Hello {message} from {self.name}!"
    
    with PyV8.JSContext() as ctx:
        # 将Python对象传递给JavaScript
        ctx.locals.pyObj = PythonObject("Python")
        
        # JavaScript调用Python方法
        js_code = """
        var greeting = pyObj.greet("JavaScript");
        greeting;
        """
        
        result = ctx.eval(js_code)
        print("JavaScript调用Python方法结果:", result)

# 运行示例
run_javascript_in_python()
python_js_interaction()
'''
    
    print("PyV8代码示例:")
    print(pyv8_code)
    
    print("\n注意:")
    print("PyV8项目已不再维护，推荐使用其他替代方案：")
    print("- PyExecJS: 支持多种JavaScript引擎")
    print("- Selenium: 用于Web自动化")
    print("- Node.js子进程: 通过subprocess调用")

pyv8_example()
```

### 2. 使用PyExecJS / Using PyExecJS

```python
def pyexecjs_example():
    """PyExecJS使用示例"""
    
    print("=== PyExecJS示例 ===")
    
    pyexecjs_code = '''
import execjs

def use_execjs():
    """使用PyExecJS执行JavaScript"""
    
    # 检查可用的JavaScript引擎
    print("可用的JavaScript引擎:", execjs.get().name)
    
    # 执行简单的JavaScript代码
    result = execjs.eval("1 + 2 + 3")
    print("简单计算结果:", result)
    
    # 编译JavaScript代码
    js_code = """
    function add(a, b) {
        return a + b;
    }
    
    function multiply(a, b) {
        return a * b;
    }
    
    function processData(data) {
        return data.map(function(item) {
            return {
                original: item,
                squared: item * item,
                doubled: item * 2
            };
        });
    }
    """
    
    ctx = execjs.compile(js_code)
    
    # 调用JavaScript函数
    add_result = ctx.call("add", 10, 20)
    multiply_result = ctx.call("multiply", 5, 6)
    
    print("加法结果:", add_result)
    print("乘法结果:", multiply_result)
    
    # 传递复杂数据
    data = [1, 2, 3, 4, 5]
    processed = ctx.call("processData", data)
    print("处理后的数据:", processed)

def integrate_with_npm_package():
    """集成NPM包示例"""
    
    # 假设我们要使用lodash库
    js_with_lodash = """
    var _ = require('lodash');
    
    function processWithLodash(data) {
        return _.chain(data)
                .filter(function(n) { return n % 2 === 0; })
                .map(function(n) { return n * n; })
                .value();
    }
    """
    
    # 注意：这需要在有lodash的Node.js环境中运行
    print("集成NPM包的JavaScript代码:")
    print(js_with_lodash)
    
    print("\\n使用方法:")
    print("1. 安装Node.js和npm")
    print("2. npm install lodash")
    print("3. 使用execjs.get('Node')指定Node.js引擎")

# 运行示例
use_execjs()
integrate_with_npm_package()
'''
    
    print("PyExecJS代码示例:")
    print(pyexecjs_code)
    
    print("\n安装PyExecJS:")
    print("pip install PyExecJS")
    
    print("\n支持的JavaScript引擎:")
    print("- Node.js")
    print("- PhantomJS") 
    print("- Nashorn (Java 8+)")
    print("- V8 (需要PyV8)")

pyexecjs_example()
```

## 性能比较和选择建议 / Performance Comparison and Selection Guidelines

```python
def performance_comparison():
    """性能比较和选择建议"""
    
    print("=== 性能比较和选择建议 ===")
    
    comparison_data = {
        "集成方式": ["纯Python", "ctypes", "C扩展", "Cython", "NumPy", "多进程"],
        "开发难度": ["简单", "中等", "困难", "中等", "简单", "中等"],
        "性能提升": ["1x", "2-5x", "10-100x", "5-50x", "10-1000x", "2-8x"],
        "适用场景": [
            "原型开发、业务逻辑",
            "调用现有C库",
            "性能关键代码",
            "Python代码优化",
            "数值计算",
            "CPU密集型任务"
        ]
    }
    
    print("1. 性能和开发难度比较:")
    for i in range(len(comparison_data["集成方式"])):
        method = comparison_data["集成方式"][i]
        difficulty = comparison_data["开发难度"][i]
        performance = comparison_data["性能提升"][i]
        use_case = comparison_data["适用场景"][i]
        
        print(f"  {method}:")
        print(f"    开发难度: {difficulty}")
        print(f"    性能提升: {performance}")
        print(f"    适用场景: {use_case}")
        print()
    
    print("2. 选择建议:")
    
    guidelines = [
        ("数值计算", "优先使用NumPy、SciPy等科学计算库"),
        ("调用现有C/C++库", "使用ctypes或编写Python绑定"),
        ("性能关键的算法", "使用Cython或编写C扩展"),
        ("CPU密集型任务", "使用多进程或异步编程"),
        ("与Java系统集成", "使用JPype或Jython"),
        ("Web前端交互", "使用PyExecJS或Selenium"),
        ("快速原型开发", "使用纯Python，后期优化热点代码")
    ]
    
    for scenario, recommendation in guidelines:
        print(f"  {scenario}: {recommendation}")
    
    print("\n3. 优化策略:")
    strategies = [
        "先用Python实现，再优化瓶颈",
        "使用性能分析工具找出热点代码",
        "考虑算法优化而不仅仅是语言优化",
        "利用现有的高性能库",
        "在开发效率和性能之间找平衡"
    ]
    
    for i, strategy in enumerate(strategies, 1):
        print(f"  {i}. {strategy}")

performance_comparison()
```

## 总结 / Summary

Python与其他语言的集成为开发者提供了强大的工具：

**主要集成方式：**
1. **C/C++集成** - 通过ctypes、C扩展、Cython实现高性能计算
2. **Java集成** - 通过Jython、JPype利用Java生态系统
3. **JavaScript集成** - 通过PyExecJS等工具实现前后端交互

**选择原则：**
- 根据性能需求选择集成方式
- 考虑开发和维护成本
- 利用现有库和生态系统
- 在开发效率和性能之间找平衡

**最佳实践：**
- 先用Python实现原型
- 识别性能瓶颈
- 选择合适的优化方案
- 保持代码的可维护性

Python的"胶水语言"特性使其能够充分利用各种语言的优势，构建高效、灵活的应用系统。

---

*注意：某些示例代码需要安装相应的库和运行环境才能执行。*
