#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
条件语句 (Conditional Statements) - if, elif, else
Conditional Statements - if, elif, else

条件语句用于根据不同条件执行不同的代码块。
Conditional statements are used to execute different code blocks based on different conditions.
"""

def basic_if_statements():
    """基本if语句 / Basic if Statements"""
    print("=== 基本if语句 Basic if Statements ===")
    
    # 简单if语句 / Simple if statement
    age = 18
    if age >= 18:
        print(f"年龄{age}岁，已成年")
    
    # if-else语句 / if-else statement
    temperature = 25
    if temperature > 30:
        print("天气很热")
    else:
        print("天气不算热")
    
    # if-elif-else语句 / if-elif-else statement
    score = 85
    if score >= 90:
        grade = "A"
    elif score >= 80:
        grade = "B"
    elif score >= 70:
        grade = "C"
    elif score >= 60:
        grade = "D"
    else:
        grade = "F"
    
    print(f"分数{score}，等级{grade}")

def comparison_operators():
    """比较运算符 / Comparison Operators"""
    print("\n=== 比较运算符 Comparison Operators ===")
    
    a, b = 10, 20
    print(f"a = {a}, b = {b}")
    
    # 基本比较运算符 / Basic comparison operators
    print(f"a == b: {a == b}")  # 等于 / Equal to
    print(f"a != b: {a != b}")  # 不等于 / Not equal to
    print(f"a < b: {a < b}")    # 小于 / Less than
    print(f"a <= b: {a <= b}")  # 小于等于 / Less than or equal to
    print(f"a > b: {a > b}")    # 大于 / Greater than
    print(f"a >= b: {a >= b}")  # 大于等于 / Greater than or equal to
    
    # 字符串比较 / String comparison
    name1, name2 = "Alice", "Bob"
    print(f"\n字符串比较:")
    print(f"'{name1}' == '{name2}': {name1 == name2}")
    print(f"'{name1}' < '{name2}': {name1 < name2}")  # 按字典序比较
    
    # 列表比较 / List comparison
    list1, list2 = [1, 2, 3], [1, 2, 4]
    print(f"\n列表比较:")
    print(f"{list1} == {list2}: {list1 == list2}")
    print(f"{list1} < {list2}: {list1 < list2}")  # 逐元素比较

def logical_operators():
    """逻辑运算符 / Logical Operators"""
    print("\n=== 逻辑运算符 Logical Operators ===")
    
    # and运算符 / and operator
    age = 25
    has_license = True
    
    if age >= 18 and has_license:
        print("可以开车")
    else:
        print("不能开车")
    
    # or运算符 / or operator
    is_weekend = True
    is_holiday = False
    
    if is_weekend or is_holiday:
        print("可以休息")
    else:
        print("需要工作")
    
    # not运算符 / not operator
    is_raining = False
    if not is_raining:
        print("天气晴朗，可以出门")
    
    # 复合逻辑表达式 / Complex logical expressions
    temperature = 25
    humidity = 60
    
    if temperature > 20 and temperature < 30 and humidity < 70:
        print("天气舒适")
    elif temperature <= 20 or temperature >= 30:
        print("温度不适宜")
    elif humidity >= 70:
        print("湿度太高")

def membership_operators():
    """成员运算符 / Membership Operators"""
    print("\n=== 成员运算符 Membership Operators ===")
    
    # in运算符 / in operator
    fruits = ["苹果", "香蕉", "橙子"]
    fruit = "苹果"
    
    if fruit in fruits:
        print(f"{fruit}在水果列表中")
    
    # not in运算符 / not in operator
    if "西瓜" not in fruits:
        print("西瓜不在水果列表中")
    
    # 字符串中的成员检查 / Membership in strings
    text = "Python编程很有趣"
    if "Python" in text:
        print("文本包含Python")
    
    # 字典中的成员检查 / Membership in dictionaries
    student = {"姓名": "张三", "年龄": 20, "专业": "计算机"}
    if "姓名" in student:
        print(f"学生姓名: {student['姓名']}")

def identity_operators():
    """身份运算符 / Identity Operators"""
    print("\n=== 身份运算符 Identity Operators ===")
    
    # is运算符 / is operator
    a = [1, 2, 3]
    b = a  # b指向同一个对象 / b points to the same object
    c = [1, 2, 3]  # c是新对象 / c is a new object
    
    print(f"a is b: {a is b}")  # True，同一个对象
    print(f"a is c: {a is c}")  # False，不同对象
    print(f"a == c: {a == c}")  # True，内容相同
    
    # is not运算符 / is not operator
    if a is not c:
        print("a和c不是同一个对象")
    
    # None检查 / None checking
    value = None
    if value is None:
        print("值为None")
    
    # 布尔值检查 / Boolean checking
    flag = True
    if flag is True:
        print("标志为True")

def conditional_expressions():
    """条件表达式(三元运算符) / Conditional Expressions (Ternary Operator)"""
    print("\n=== 条件表达式 Conditional Expressions ===")
    
    # 基本三元运算符 / Basic ternary operator
    age = 20
    status = "成年人" if age >= 18 else "未成年人"
    print(f"年龄{age}，状态: {status}")
    
    # 数值处理 / Numeric processing
    x = -5
    absolute_value = x if x >= 0 else -x
    print(f"{x}的绝对值: {absolute_value}")
    
    # 列表处理 / List processing
    numbers = [1, 2, 3, 4, 5]
    result = "有数据" if numbers else "无数据"
    print(f"列表状态: {result}")
    
    # 嵌套条件表达式 / Nested conditional expressions
    score = 85
    grade = "A" if score >= 90 else "B" if score >= 80 else "C" if score >= 70 else "D"
    print(f"分数{score}，等级{grade}")

def practical_examples():
    """实际应用示例 / Practical Examples"""
    print("\n=== 实际应用示例 Practical Examples ===")
    
    # 1. 用户登录验证 / User login validation
    print("1. 用户登录验证示例:")
    
    def validate_login(username, password):
        """验证用户登录 / Validate user login"""
        valid_users = {
            "admin": "123456",
            "user1": "password",
            "user2": "secret"
        }
        
        if username in valid_users:
            if valid_users[username] == password:
                return "登录成功"
            else:
                return "密码错误"
        else:
            return "用户不存在"
    
    # 测试登录 / Test login
    print(validate_login("admin", "123456"))
    print(validate_login("admin", "wrong"))
    print(validate_login("unknown", "123"))
    
    # 2. 成绩等级判定 / Grade classification
    print("\n2. 成绩等级判定:")
    
    def get_grade(score):
        """根据分数获取等级 / Get grade based on score"""
        if not isinstance(score, (int, float)):
            return "无效分数"
        
        if score < 0 or score > 100:
            return "分数超出范围"
        elif score >= 90:
            return "优秀"
        elif score >= 80:
            return "良好"
        elif score >= 70:
            return "中等"
        elif score >= 60:
            return "及格"
        else:
            return "不及格"
    
    # 测试成绩判定 / Test grade classification
    test_scores = [95, 85, 75, 65, 55, 105, -10, "abc"]
    for score in test_scores:
        print(f"分数{score}: {get_grade(score)}")
    
    # 3. 购物折扣计算 / Shopping discount calculation
    print("\n3. 购物折扣计算:")
    
    def calculate_discount(amount, is_member, is_first_purchase):
        """计算购物折扣 / Calculate shopping discount"""
        discount = 0
        
        # 会员折扣 / Member discount
        if is_member:
            discount += 0.1  # 10%会员折扣
        
        # 首次购买折扣 / First purchase discount
        if is_first_purchase:
            discount += 0.05  # 5%首购折扣
        
        # 大额购买折扣 / Large amount discount
        if amount >= 1000:
            discount += 0.05  # 5%大额折扣
        elif amount >= 500:
            discount += 0.03  # 3%中额折扣
        
        # 最大折扣限制 / Maximum discount limit
        discount = min(discount, 0.2)  # 最大20%折扣
        
        final_amount = amount * (1 - discount)
        return final_amount, discount * 100
    
    # 测试折扣计算 / Test discount calculation
    test_cases = [
        (300, False, False),
        (600, True, False),
        (1200, True, True),
        (800, False, True)
    ]
    
    for amount, is_member, is_first in test_cases:
        final, discount_percent = calculate_discount(amount, is_member, is_first)
        print(f"原价{amount}元，会员:{is_member}，首购:{is_first} -> "
              f"折扣{discount_percent:.1f}%，实付{final:.2f}元")

def advanced_conditions():
    """高级条件判断 / Advanced Conditional Statements"""
    print("\n=== 高级条件判断 Advanced Conditional Statements ===")
    
    # 1. 多条件组合 / Multiple condition combinations
    def check_weather_activity(temperature, humidity, wind_speed, is_sunny):
        """根据天气条件推荐活动 / Recommend activity based on weather"""
        if is_sunny and 20 <= temperature <= 28 and humidity < 60 and wind_speed < 15:
            return "完美的户外运动天气"
        elif temperature > 35 or humidity > 80:
            return "建议室内活动"
        elif wind_speed > 25:
            return "风太大，不适合户外活动"
        elif temperature < 10:
            return "天气太冷，注意保暖"
        else:
            return "天气一般，可以考虑轻度户外活动"
    
    # 测试天气判断 / Test weather conditions
    weather_conditions = [
        (25, 50, 10, True),   # 完美天气
        (38, 70, 5, True),    # 太热
        (22, 45, 30, True),   # 风大
        (5, 40, 8, False),    # 太冷
        (18, 65, 12, False)   # 一般
    ]
    
    for temp, hum, wind, sunny in weather_conditions:
        activity = check_weather_activity(temp, hum, wind, sunny)
        print(f"温度{temp}°C，湿度{hum}%，风速{wind}km/h，晴天:{sunny} -> {activity}")
    
    # 2. 短路求值 / Short-circuit evaluation
    print("\n短路求值示例:")
    
    def safe_divide(a, b):
        """安全除法 / Safe division"""
        # 利用短路求值避免除零错误 / Use short-circuit to avoid division by zero
        return b != 0 and a / b
    
    print(f"10 / 2 = {safe_divide(10, 2)}")
    print(f"10 / 0 = {safe_divide(10, 0)}")
    
    # 3. 条件链 / Condition chaining
    def classify_number(num):
        """数字分类 / Number classification"""
        if num > 0:
            if num % 2 == 0:
                if num > 100:
                    return "大偶数"
                else:
                    return "小偶数"
            else:
                if num > 100:
                    return "大奇数"
                else:
                    return "小奇数"
        elif num < 0:
            return "负数"
        else:
            return "零"
    
    test_numbers = [150, 25, 88, 7, -10, 0]
    for num in test_numbers:
        print(f"{num}: {classify_number(num)}")

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python条件语句学习示例")
    print("Python Conditional Statements Learning Examples")
    print("=" * 60)
    
    basic_if_statements()
    comparison_operators()
    logical_operators()
    membership_operators()
    identity_operators()
    conditional_expressions()
    practical_examples()
    advanced_conditions()
    
    print("\n" + "=" * 60)
    print("条件语句学习完成！Conditional statements learning completed!")
