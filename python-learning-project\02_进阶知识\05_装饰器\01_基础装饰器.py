#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
装饰器 (Decorators) - Python高级特性
Decorators - Python Advanced Feature

装饰器是Python中用于修改或扩展函数或类功能的强大工具。
Decorators are powerful tools in Python for modifying or extending the functionality of functions or classes.
"""

import time
import functools
from typing import Callable, Any

def basic_decorator_concept():
    """装饰器基本概念 / Basic Decorator Concept"""
    print("=== 装饰器基本概念 Basic Decorator Concept ===")
    
    # 函数是第一类对象 / Functions are first-class objects
    def greet(name):
        return f"Hello, {name}!"
    
    # 将函数赋值给变量 / Assign function to variable
    say_hello = greet
    print(f"函数调用: {say_hello('Alice')}")
    
    # 函数作为参数传递 / Pass function as parameter
    def call_function(func, arg):
        return func(arg)
    
    result = call_function(greet, "Bob")
    print(f"函数作为参数: {result}")
    
    # 函数内部定义函数 / Define function inside function
    def outer_function(name):
        def inner_function():
            return f"Inner says hello to {name}"
        return inner_function
    
    inner = outer_function("Charlie")
    print(f"内部函数: {inner()}")

def simple_decorator():
    """简单装饰器 / Simple Decorator"""
    print("\n=== 简单装饰器 Simple Decorator ===")
    
    # 定义装饰器 / Define decorator
    def my_decorator(func):
        def wrapper():
            print("装饰器：函数执行前")
            result = func()
            print("装饰器：函数执行后")
            return result
        return wrapper
    
    # 使用装饰器 / Use decorator
    @my_decorator
    def say_hello():
        print("Hello from decorated function!")
        return "Hello"
    
    print("调用被装饰的函数:")
    result = say_hello()
    print(f"返回值: {result}")
    
    # 手动装饰 / Manual decoration
    def say_goodbye():
        print("Goodbye!")
        return "Goodbye"
    
    decorated_goodbye = my_decorator(say_goodbye)
    print("\n手动装饰:")
    decorated_goodbye()

def decorator_with_arguments():
    """带参数的装饰器 / Decorator with Arguments"""
    print("\n=== 带参数的装饰器 Decorator with Arguments ===")
    
    # 装饰带参数的函数 / Decorate function with arguments
    def log_calls(func):
        def wrapper(*args, **kwargs):
            print(f"调用函数: {func.__name__}")
            print(f"位置参数: {args}")
            print(f"关键字参数: {kwargs}")
            result = func(*args, **kwargs)
            print(f"返回值: {result}")
            return result
        return wrapper
    
    @log_calls
    def add(a, b):
        """加法函数"""
        return a + b
    
    @log_calls
    def greet(name, greeting="Hello"):
        """问候函数"""
        return f"{greeting}, {name}!"
    
    print("调用add函数:")
    add(3, 5)
    
    print("\n调用greet函数:")
    greet("Alice", greeting="Hi")

def timing_decorator():
    """计时装饰器 / Timing Decorator"""
    print("\n=== 计时装饰器 Timing Decorator ===")
    
    def timer(func):
        """计算函数执行时间的装饰器"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            execution_time = end_time - start_time
            print(f"函数 {func.__name__} 执行时间: {execution_time:.6f}秒")
            return result
        return wrapper
    
    @timer
    def slow_function():
        """模拟耗时操作"""
        time.sleep(0.1)  # 休眠0.1秒
        return "操作完成"
    
    @timer
    def calculate_sum(n):
        """计算1到n的和"""
        return sum(range(1, n + 1))
    
    print("执行耗时函数:")
    slow_function()
    
    print("\n计算求和:")
    result = calculate_sum(1000000)
    print(f"结果: {result}")

def caching_decorator():
    """缓存装饰器 / Caching Decorator"""
    print("\n=== 缓存装饰器 Caching Decorator ===")
    
    def memoize(func):
        """缓存函数结果的装饰器"""
        cache = {}
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 创建缓存键 / Create cache key
            key = str(args) + str(sorted(kwargs.items()))
            
            if key in cache:
                print(f"缓存命中: {func.__name__}{args}")
                return cache[key]
            
            print(f"计算结果: {func.__name__}{args}")
            result = func(*args, **kwargs)
            cache[key] = result
            return result
        
        # 添加清除缓存的方法 / Add cache clearing method
        wrapper.clear_cache = lambda: cache.clear()
        wrapper.cache_info = lambda: f"缓存大小: {len(cache)}"
        
        return wrapper
    
    @memoize
    def fibonacci(n):
        """斐波那契数列"""
        if n <= 1:
            return n
        return fibonacci(n - 1) + fibonacci(n - 2)
    
    @memoize
    def expensive_calculation(x, y):
        """模拟昂贵的计算"""
        time.sleep(0.1)  # 模拟计算时间
        return x ** y
    
    print("计算斐波那契数列:")
    print(f"fibonacci(10) = {fibonacci(10)}")
    print(f"fibonacci(10) = {fibonacci(10)}")  # 第二次调用使用缓存
    
    print(f"\n{fibonacci.cache_info()}")
    
    print("\n昂贵计算:")
    print(f"2^10 = {expensive_calculation(2, 10)}")
    print(f"2^10 = {expensive_calculation(2, 10)}")  # 使用缓存

def parametrized_decorator():
    """参数化装饰器 / Parametrized Decorator"""
    print("\n=== 参数化装饰器 Parametrized Decorator ===")
    
    def repeat(times):
        """重复执行函数的装饰器"""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                results = []
                for i in range(times):
                    print(f"第{i+1}次执行:")
                    result = func(*args, **kwargs)
                    results.append(result)
                return results
            return wrapper
        return decorator
    
    def validate_types(**expected_types):
        """类型验证装饰器"""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                # 验证参数类型 / Validate argument types
                import inspect
                sig = inspect.signature(func)
                bound_args = sig.bind(*args, **kwargs)
                bound_args.apply_defaults()
                
                for param_name, expected_type in expected_types.items():
                    if param_name in bound_args.arguments:
                        value = bound_args.arguments[param_name]
                        if not isinstance(value, expected_type):
                            raise TypeError(
                                f"参数 {param_name} 期望类型 {expected_type.__name__}, "
                                f"实际类型 {type(value).__name__}"
                            )
                
                return func(*args, **kwargs)
            return wrapper
        return decorator
    
    @repeat(3)
    def say_hello(name):
        print(f"Hello, {name}!")
        return f"Greeted {name}"
    
    @validate_types(x=int, y=int)
    def multiply(x, y):
        return x * y
    
    print("重复执行装饰器:")
    results = say_hello("Alice")
    print(f"所有结果: {results}")
    
    print("\n类型验证装饰器:")
    try:
        result = multiply(5, 3)
        print(f"5 * 3 = {result}")
        
        # 这会引发类型错误 / This will raise a type error
        multiply(5, "3")
    except TypeError as e:
        print(f"类型错误: {e}")

def class_decorator():
    """类装饰器 / Class Decorator"""
    print("\n=== 类装饰器 Class Decorator ===")
    
    class CountCalls:
        """计算函数调用次数的类装饰器"""
        def __init__(self, func):
            self.func = func
            self.count = 0
            functools.update_wrapper(self, func)
        
        def __call__(self, *args, **kwargs):
            self.count += 1
            print(f"函数 {self.func.__name__} 被调用第 {self.count} 次")
            return self.func(*args, **kwargs)
        
        def reset_count(self):
            """重置计数器"""
            self.count = 0
    
    @CountCalls
    def greet(name):
        return f"Hello, {name}!"
    
    # 测试类装饰器 / Test class decorator
    print(greet("Alice"))
    print(greet("Bob"))
    print(greet("Charlie"))
    
    print(f"总调用次数: {greet.count}")
    greet.reset_count()
    print(f"重置后计数: {greet.count}")

def decorator_chain():
    """装饰器链 / Decorator Chain"""
    print("\n=== 装饰器链 Decorator Chain ===")
    
    def bold(func):
        """加粗装饰器"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            return f"**{result}**"
        return wrapper
    
    def italic(func):
        """斜体装饰器"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            return f"*{result}*"
        return wrapper
    
    def uppercase(func):
        """大写装饰器"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            return result.upper()
        return wrapper
    
    # 多个装饰器的应用顺序：从下到上 / Multiple decorators applied bottom to top
    @bold
    @italic
    @uppercase
    def format_text(text):
        return text
    
    result = format_text("hello world")
    print(f"装饰器链结果: {result}")
    
    # 等价于 / Equivalent to:
    # format_text = bold(italic(uppercase(format_text)))

def practical_decorators():
    """实用装饰器示例 / Practical Decorator Examples"""
    print("\n=== 实用装饰器示例 Practical Decorator Examples ===")
    
    # 1. 权限检查装饰器 / Permission check decorator
    def require_permission(permission):
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                # 模拟用户权限检查 / Simulate user permission check
                user_permissions = {"read", "write"}  # 模拟当前用户权限
                
                if permission not in user_permissions:
                    raise PermissionError(f"需要 {permission} 权限")
                
                return func(*args, **kwargs)
            return wrapper
        return decorator
    
    # 2. 重试装饰器 / Retry decorator
    def retry(max_attempts=3, delay=1):
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                for attempt in range(max_attempts):
                    try:
                        return func(*args, **kwargs)
                    except Exception as e:
                        if attempt == max_attempts - 1:
                            raise e
                        print(f"第{attempt + 1}次尝试失败: {e}")
                        time.sleep(delay)
            return wrapper
        return decorator
    
    @require_permission("read")
    def read_data():
        return "读取数据成功"
    
    @require_permission("admin")
    def delete_data():
        return "删除数据成功"
    
    @retry(max_attempts=3, delay=0.1)
    def unreliable_function():
        import random
        if random.random() < 0.7:  # 70%概率失败
            raise Exception("随机失败")
        return "成功执行"
    
    # 测试权限装饰器 / Test permission decorator
    try:
        print(read_data())  # 有权限
    except PermissionError as e:
        print(f"权限错误: {e}")
    
    try:
        print(delete_data())  # 无权限
    except PermissionError as e:
        print(f"权限错误: {e}")
    
    # 测试重试装饰器 / Test retry decorator
    try:
        result = unreliable_function()
        print(f"重试成功: {result}")
    except Exception as e:
        print(f"重试失败: {e}")

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python装饰器学习示例")
    print("Python Decorators Learning Examples")
    print("=" * 50)
    
    basic_decorator_concept()
    simple_decorator()
    decorator_with_arguments()
    timing_decorator()
    caching_decorator()
    parametrized_decorator()
    class_decorator()
    decorator_chain()
    practical_decorators()
    
    print("\n" + "=" * 50)
    print("装饰器学习完成！Decorators learning completed!")
