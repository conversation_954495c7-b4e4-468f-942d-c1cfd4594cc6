#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义函数 (Custom Functions) - Python函数定义和使用
Custom Functions - Python Function Definition and Usage

函数是组织代码的基本单位，用于封装可重用的代码块，提高代码的模块化和可维护性。
Functions are basic units for organizing code, used to encapsulate reusable code blocks and improve code modularity and maintainability.
"""

import math
from typing import List, Optional, Union, Callable

def basic_function_definition():
    """基本函数定义 / Basic Function Definition"""
    print("=== 基本函数定义 Basic Function Definition ===")
    
    # 1. 最简单的函数 / Simplest function
    def greet():
        """简单的问候函数"""
        print("Hello, World!")
    
    print("1. 调用简单函数:")
    greet()
    
    # 2. 带参数的函数 / Function with parameters
    def greet_person(name):
        """带参数的问候函数"""
        print(f"Hello, {name}!")
    
    print("\n2. 调用带参数的函数:")
    greet_person("Alice")
    greet_person("Bob")
    
    # 3. 带返回值的函数 / Function with return value
    def add_numbers(a, b):
        """加法函数"""
        result = a + b
        return result
    
    print("\n3. 调用带返回值的函数:")
    sum_result = add_numbers(5, 3)
    print(f"5 + 3 = {sum_result}")
    
    # 4. 带默认参数的函数 / Function with default parameters
    def greet_with_title(name, title="先生"):
        """带默认参数的问候函数"""
        return f"您好，{title} {name}！"
    
    print("\n4. 调用带默认参数的函数:")
    print(greet_with_title("张三"))
    print(greet_with_title("李四", "女士"))

def function_parameters():
    """函数参数详解 / Function Parameters Explained"""
    print("\n=== 函数参数详解 Function Parameters Explained ===")
    
    # 1. 位置参数 / Positional arguments
    def calculate_rectangle_area(length, width):
        """计算矩形面积"""
        return length * width
    
    print("1. 位置参数:")
    area = calculate_rectangle_area(5, 3)
    print(f"矩形面积: {area}")
    
    # 2. 关键字参数 / Keyword arguments
    print("\n2. 关键字参数:")
    area = calculate_rectangle_area(width=4, length=6)
    print(f"矩形面积: {area}")
    
    # 3. 默认参数 / Default arguments
    def power(base, exponent=2):
        """计算幂次"""
        return base ** exponent
    
    print("\n3. 默认参数:")
    print(f"3的平方: {power(3)}")
    print(f"2的立方: {power(2, 3)}")
    
    # 4. 可变位置参数 (*args) / Variable positional arguments
    def sum_all(*numbers):
        """计算所有数字的和"""
        total = 0
        for num in numbers:
            total += num
        return total
    
    print("\n4. 可变位置参数 (*args):")
    print(f"sum_all(1, 2, 3): {sum_all(1, 2, 3)}")
    print(f"sum_all(1, 2, 3, 4, 5): {sum_all(1, 2, 3, 4, 5)}")
    
    # 5. 可变关键字参数 (**kwargs) / Variable keyword arguments
    def create_profile(**info):
        """创建用户档案"""
        profile = "用户档案:\n"
        for key, value in info.items():
            profile += f"  {key}: {value}\n"
        return profile
    
    print("\n5. 可变关键字参数 (**kwargs):")
    profile = create_profile(name="张三", age=25, city="北京", job="程序员")
    print(profile)
    
    # 6. 混合参数 / Mixed parameters
    def complex_function(required, default="默认值", *args, **kwargs):
        """复杂参数函数"""
        print(f"必需参数: {required}")
        print(f"默认参数: {default}")
        print(f"位置参数: {args}")
        print(f"关键字参数: {kwargs}")
    
    print("6. 混合参数:")
    complex_function("必需", "自定义", 1, 2, 3, name="测试", value=42)

def return_values():
    """返回值详解 / Return Values Explained"""
    print("\n=== 返回值详解 Return Values Explained ===")
    
    # 1. 单个返回值 / Single return value
    def get_square(x):
        """返回平方值"""
        return x * x
    
    print("1. 单个返回值:")
    result = get_square(4)
    print(f"4的平方: {result}")
    
    # 2. 多个返回值 / Multiple return values
    def get_circle_info(radius):
        """返回圆的周长和面积"""
        circumference = 2 * math.pi * radius
        area = math.pi * radius * radius
        return circumference, area
    
    print("\n2. 多个返回值:")
    c, a = get_circle_info(5)
    print(f"半径为5的圆: 周长={c:.2f}, 面积={a:.2f}")
    
    # 也可以作为元组接收 / Can also receive as tuple
    circle_info = get_circle_info(3)
    print(f"半径为3的圆信息: {circle_info}")
    
    # 3. 条件返回 / Conditional return
    def divide_safely(a, b):
        """安全除法"""
        if b == 0:
            return None, "除数不能为零"
        else:
            return a / b, "计算成功"
    
    print("\n3. 条件返回:")
    result, message = divide_safely(10, 2)
    print(f"10 ÷ 2 = {result}, {message}")
    
    result, message = divide_safely(10, 0)
    print(f"10 ÷ 0 = {result}, {message}")
    
    # 4. 返回函数 / Return function
    def create_multiplier(factor):
        """创建乘法器函数"""
        def multiplier(x):
            return x * factor
        return multiplier
    
    print("\n4. 返回函数:")
    double = create_multiplier(2)
    triple = create_multiplier(3)
    
    print(f"double(5) = {double(5)}")
    print(f"triple(4) = {triple(4)}")

def variable_scope():
    """变量作用域 / Variable Scope"""
    print("\n=== 变量作用域 Variable Scope ===")
    
    # 全局变量 / Global variable
    global_var = "我是全局变量"
    
    def scope_demo():
        # 局部变量 / Local variable
        local_var = "我是局部变量"
        print(f"函数内部 - 全局变量: {global_var}")
        print(f"函数内部 - 局部变量: {local_var}")
    
    print("1. 基本作用域:")
    scope_demo()
    print(f"函数外部 - 全局变量: {global_var}")
    # print(local_var)  # 这会报错，因为局部变量在函数外不可访问
    
    # 修改全局变量 / Modify global variable
    def modify_global():
        global global_var
        global_var = "修改后的全局变量"
        print(f"函数内修改全局变量: {global_var}")
    
    print("\n2. 修改全局变量:")
    print(f"修改前: {global_var}")
    modify_global()
    print(f"修改后: {global_var}")
    
    # 嵌套函数作用域 / Nested function scope
    def outer_function():
        outer_var = "外层函数变量"
        
        def inner_function():
            inner_var = "内层函数变量"
            print(f"内层函数 - 外层变量: {outer_var}")
            print(f"内层函数 - 内层变量: {inner_var}")
        
        inner_function()
        print(f"外层函数 - 外层变量: {outer_var}")
        # print(inner_var)  # 这会报错
    
    print("\n3. 嵌套函数作用域:")
    outer_function()
    
    # nonlocal 关键字 / nonlocal keyword
    def outer_with_nonlocal():
        count = 0
        
        def increment():
            nonlocal count
            count += 1
            print(f"计数器: {count}")
        
        increment()
        increment()
        increment()
        return count
    
    print("\n4. nonlocal 关键字:")
    final_count = outer_with_nonlocal()
    print(f"最终计数: {final_count}")

def lambda_functions():
    """Lambda函数 / Lambda Functions"""
    print("\n=== Lambda函数 Lambda Functions ===")
    
    # 1. 基本lambda函数 / Basic lambda function
    print("1. 基本lambda函数:")
    square = lambda x: x ** 2
    print(f"lambda平方函数: square(5) = {square(5)}")
    
    # 等价的普通函数 / Equivalent regular function
    def square_regular(x):
        return x ** 2
    
    print(f"普通平方函数: square_regular(5) = {square_regular(5)}")
    
    # 2. 多参数lambda / Multi-parameter lambda
    print("\n2. 多参数lambda:")
    add = lambda x, y: x + y
    multiply = lambda x, y, z: x * y * z
    
    print(f"add(3, 4) = {add(3, 4)}")
    print(f"multiply(2, 3, 4) = {multiply(2, 3, 4)}")
    
    # 3. 在高阶函数中使用lambda / Using lambda in higher-order functions
    print("\n3. 在高阶函数中使用lambda:")
    
    numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    
    # map函数 / map function
    squares = list(map(lambda x: x**2, numbers))
    print(f"平方: {squares}")
    
    # filter函数 / filter function
    evens = list(filter(lambda x: x % 2 == 0, numbers))
    print(f"偶数: {evens}")
    
    # sorted函数 / sorted function
    words = ["apple", "banana", "cherry", "date"]
    sorted_by_length = sorted(words, key=lambda x: len(x))
    print(f"按长度排序: {sorted_by_length}")
    
    # 4. lambda的限制 / Lambda limitations
    print("\n4. lambda函数的限制:")
    print("- 只能包含表达式，不能包含语句")
    print("- 不能包含赋值、print等语句")
    print("- 适合简单的单行函数")
    print("- 复杂逻辑应该使用普通函数")

def higher_order_functions():
    """高阶函数 / Higher-Order Functions"""
    print("\n=== 高阶函数 Higher-Order Functions ===")
    
    # 1. 函数作为参数 / Function as parameter
    def apply_operation(x, y, operation):
        """应用操作函数"""
        return operation(x, y)
    
    def add(a, b):
        return a + b
    
    def multiply(a, b):
        return a * b
    
    print("1. 函数作为参数:")
    result1 = apply_operation(5, 3, add)
    result2 = apply_operation(5, 3, multiply)
    print(f"apply_operation(5, 3, add) = {result1}")
    print(f"apply_operation(5, 3, multiply) = {result2}")
    
    # 2. 函数作为返回值 / Function as return value
    def create_calculator(operation):
        """创建计算器函数"""
        if operation == "add":
            return lambda x, y: x + y
        elif operation == "subtract":
            return lambda x, y: x - y
        elif operation == "multiply":
            return lambda x, y: x * y
        elif operation == "divide":
            return lambda x, y: x / y if y != 0 else None
    
    print("\n2. 函数作为返回值:")
    adder = create_calculator("add")
    multiplier = create_calculator("multiply")
    
    print(f"adder(10, 5) = {adder(10, 5)}")
    print(f"multiplier(10, 5) = {multiplier(10, 5)}")
    
    # 3. 内置高阶函数 / Built-in higher-order functions
    print("\n3. 内置高阶函数:")
    
    numbers = [1, 2, 3, 4, 5]
    
    # map - 映射 / map - mapping
    doubled = list(map(lambda x: x * 2, numbers))
    print(f"map(lambda x: x * 2, {numbers}) = {doubled}")
    
    # filter - 过滤 / filter - filtering
    odds = list(filter(lambda x: x % 2 == 1, numbers))
    print(f"filter(lambda x: x % 2 == 1, {numbers}) = {odds}")
    
    # reduce - 归约 / reduce - reduction
    from functools import reduce
    sum_all = reduce(lambda x, y: x + y, numbers)
    print(f"reduce(lambda x, y: x + y, {numbers}) = {sum_all}")
    
    # 4. 自定义高阶函数 / Custom higher-order function
    def process_list(data, *functions):
        """处理列表的高阶函数"""
        result = data
        for func in functions:
            result = func(result)
        return result
    
    print("\n4. 自定义高阶函数:")
    data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    
    # 定义处理函数 / Define processing functions
    filter_evens = lambda lst: list(filter(lambda x: x % 2 == 0, lst))
    square_all = lambda lst: list(map(lambda x: x ** 2, lst))
    sum_all = lambda lst: sum(lst)
    
    result = process_list(data, filter_evens, square_all, sum_all)
    print(f"处理结果: {result}")
    print("处理步骤: 原数据 -> 过滤偶数 -> 平方 -> 求和")

def practical_functions():
    """实用函数示例 / Practical Function Examples"""
    print("\n=== 实用函数示例 Practical Function Examples ===")
    
    # 1. 数据验证函数 / Data validation functions
    def validate_email(email):
        """简单的邮箱验证"""
        return "@" in email and "." in email.split("@")[-1]
    
    def validate_phone(phone):
        """简单的手机号验证"""
        return phone.isdigit() and len(phone) == 11
    
    print("1. 数据验证函数:")
    emails = ["<EMAIL>", "invalid-email", "<EMAIL>"]
    for email in emails:
        print(f"  {email}: {'有效' if validate_email(email) else '无效'}")
    
    # 2. 数据处理函数 / Data processing functions
    def clean_text(text):
        """清理文本数据"""
        return text.strip().lower().replace("  ", " ")
    
    def calculate_statistics(numbers):
        """计算统计信息"""
        if not numbers:
            return None
        
        return {
            "count": len(numbers),
            "sum": sum(numbers),
            "average": sum(numbers) / len(numbers),
            "min": min(numbers),
            "max": max(numbers)
        }
    
    print("\n2. 数据处理函数:")
    text = "  Hello World  "
    print(f"原文本: '{text}'")
    print(f"清理后: '{clean_text(text)}'")
    
    data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    stats = calculate_statistics(data)
    print(f"数据统计: {stats}")
    
    # 3. 缓存函数 / Caching function
    def memoize(func):
        """简单的缓存装饰器"""
        cache = {}
        
        def wrapper(*args):
            if args in cache:
                print(f"缓存命中: {func.__name__}{args}")
                return cache[args]
            
            result = func(*args)
            cache[args] = result
            print(f"计算结果: {func.__name__}{args} = {result}")
            return result
        
        return wrapper
    
    @memoize
    def fibonacci(n):
        """斐波那契数列"""
        if n <= 1:
            return n
        return fibonacci(n - 1) + fibonacci(n - 2)
    
    print("\n3. 缓存函数示例:")
    print(f"fibonacci(10) = {fibonacci(10)}")

def function_best_practices():
    """函数最佳实践 / Function Best Practices"""
    print("\n=== 函数最佳实践 Function Best Practices ===")
    
    print("函数设计最佳实践:")
    print("1. 单一职责原则 - 每个函数只做一件事")
    print("2. 函数名要清晰描述功能")
    print("3. 参数数量不宜过多 (建议不超过5个)")
    print("4. 使用类型提示提高代码可读性")
    print("5. 编写文档字符串说明函数用途")
    print("6. 避免修改可变参数")
    print("7. 优先使用纯函数 (无副作用)")
    
    # 示例：好的函数设计 / Example: Good function design
    def calculate_bmi(weight: float, height: float) -> dict:
        """
        计算身体质量指数 (BMI)
        
        Args:
            weight (float): 体重 (公斤)
            height (float): 身高 (米)
        
        Returns:
            dict: 包含BMI值和分类的字典
        
        Raises:
            ValueError: 当体重或身高为非正数时
        """
        if weight <= 0 or height <= 0:
            raise ValueError("体重和身高必须为正数")
        
        bmi = weight / (height ** 2)
        
        if bmi < 18.5:
            category = "偏瘦"
        elif bmi < 24:
            category = "正常"
        elif bmi < 28:
            category = "偏胖"
        else:
            category = "肥胖"
        
        return {
            "bmi": round(bmi, 2),
            "category": category
        }
    
    print("\n好的函数设计示例:")
    try:
        result = calculate_bmi(70, 1.75)
        print(f"BMI计算结果: {result}")
    except ValueError as e:
        print(f"输入错误: {e}")

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python自定义函数学习示例")
    print("Python Custom Functions Learning Examples")
    print("=" * 50)
    
    basic_function_definition()
    function_parameters()
    return_values()
    variable_scope()
    lambda_functions()
    higher_order_functions()
    practical_functions()
    function_best_practices()
    
    print("\n" + "=" * 50)
    print("自定义函数学习完成！Custom functions learning completed!")
