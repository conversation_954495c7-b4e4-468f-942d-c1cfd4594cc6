[project]
name = "python-learning-project"
version = "0.1.0"
description = "Python基础和进阶知识学习项目 - 包含完整的代码示例和练习"
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "matplotlib>=3.5.0",
    "numpy>=1.21.0",
    "requests>=2.25.0",
]
authors = [
    {name = "Python学习者", email = "<EMAIL>"}
]
keywords = ["python", "learning", "tutorial", "education", "基础", "进阶"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Education",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Education",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "black>=22.0.0",
    "flake8>=4.0.0",
]
