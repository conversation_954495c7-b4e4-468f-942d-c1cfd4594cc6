#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
元类 (Metaclasses) - Python元类机制
Metaclasses - Python Metaclass Mechanism

元类是"类的类"，用于控制类的创建过程。元类是Python中最高级的特性之一。
Metaclasses are "classes of classes" used to control the class creation process. Metaclasses are one of the most advanced features in Python.
"""

from typing import Any, Dict, Type

def understanding_metaclasses():
    """理解元类 / Understanding Metaclasses"""
    print("=== 理解元类 Understanding Metaclasses ===")
    
    print("1. 一切皆对象的概念:")
    print("   - 在Python中，一切都是对象")
    print("   - 类也是对象")
    print("   - 元类是创建类对象的类")
    
    # 创建一个简单的类 / Create a simple class
    class MyClass:
        def __init__(self, value):
            self.value = value
        
        def get_value(self):
            return self.value
    
    # 创建实例 / Create instance
    obj = MyClass(42)
    
    print(f"\n2. 对象和类的关系:")
    print(f"   obj = {obj}")
    print(f"   type(obj) = {type(obj)}")
    print(f"   obj.__class__ = {obj.__class__}")
    
    print(f"\n3. 类也是对象:")
    print(f"   MyClass = {MyClass}")
    print(f"   type(MyClass) = {type(MyClass)}")
    print(f"   MyClass.__class__ = {MyClass.__class__}")
    
    print(f"\n4. 元类的元类:")
    print(f"   type(type) = {type(type)}")
    print(f"   type.__class__ = {type.__class__}")
    
    print(f"\n5. 内置类型的元类:")
    print(f"   type(int) = {type(int)}")
    print(f"   type(str) = {type(str)}")
    print(f"   type(list) = {type(list)}")

def creating_classes_dynamically():
    """动态创建类 / Creating Classes Dynamically"""
    print("\n=== 动态创建类 Creating Classes Dynamically ===")
    
    print("1. 使用type()动态创建类:")
    
    # 定义方法 / Define methods
    def init_method(self, name):
        self.name = name
    
    def greet_method(self):
        return f"Hello, I'm {self.name}"
    
    def __repr__(self):
        return f"Person('{self.name}')"
    
    # 使用type创建类 / Create class using type
    # type(name, bases, dict)
    Person = type('Person', (), {
        '__init__': init_method,
        'greet': greet_method,
        '__repr__': __repr__,
        'species': 'Homo sapiens'  # 类属性
    })
    
    print(f"动态创建的类: {Person}")
    print(f"类的类型: {type(Person)}")
    
    # 使用动态创建的类 / Use dynamically created class
    person = Person("Alice")
    print(f"实例: {person}")
    print(f"问候: {person.greet()}")
    print(f"物种: {person.species}")
    
    print("\n2. 带继承的动态类创建:")
    
    # 基类 / Base class
    class Animal:
        def __init__(self, name):
            self.name = name
        
        def speak(self):
            return "Some sound"
    
    # 动态创建继承类 / Dynamically create inherited class
    def dog_speak(self):
        return f"{self.name} says Woof!"
    
    Dog = type('Dog', (Animal,), {
        'speak': dog_speak,
        'breed': 'Unknown'
    })
    
    dog = Dog("Buddy")
    print(f"狗实例: {dog}")
    print(f"狗说话: {dog.speak()}")
    print(f"品种: {dog.breed}")
    print(f"是否是Animal的实例: {isinstance(dog, Animal)}")

def simple_metaclass_example():
    """简单元类示例 / Simple Metaclass Example"""
    print("\n=== 简单元类示例 Simple Metaclass Example ===")
    
    print("1. 使用函数作为元类:")
    
    def simple_metaclass(name, bases, attrs):
        """简单的元类函数"""
        print(f"创建类 '{name}'")
        print(f"  基类: {bases}")
        print(f"  属性: {list(attrs.keys())}")
        
        # 为所有方法添加日志 / Add logging to all methods
        for key, value in attrs.items():
            if callable(value) and not key.startswith('__'):
                attrs[key] = add_logging(value, key)
        
        # 添加类创建时间 / Add class creation time
        import datetime
        attrs['_created_at'] = datetime.datetime.now()
        
        return type(name, bases, attrs)
    
    def add_logging(func, func_name):
        """为函数添加日志装饰器"""
        def wrapper(*args, **kwargs):
            print(f"  调用方法: {func_name}")
            result = func(*args, **kwargs)
            print(f"  方法 {func_name} 执行完成")
            return result
        return wrapper
    
    # 使用元类创建类 / Create class using metaclass
    class LoggedClass(metaclass=simple_metaclass):
        def __init__(self, value):
            self.value = value
        
        def get_value(self):
            return self.value
        
        def set_value(self, value):
            self.value = value
    
    print(f"\n类创建时间: {LoggedClass._created_at}")
    
    print("\n2. 使用带日志的类:")
    obj = LoggedClass(100)
    print(f"获取值: {obj.get_value()}")
    obj.set_value(200)
    print(f"新值: {obj.get_value()}")

def metaclass_with_class():
    """使用类作为元类 / Using Class as Metaclass"""
    print("\n=== 使用类作为元类 Using Class as Metaclass ===")
    
    class SingletonMeta(type):
        """单例模式元类"""
        
        _instances = {}
        
        def __call__(cls, *args, **kwargs):
            """控制实例创建"""
            if cls not in cls._instances:
                print(f"创建 {cls.__name__} 的第一个实例")
                cls._instances[cls] = super().__call__(*args, **kwargs)
            else:
                print(f"返回 {cls.__name__} 的现有实例")
            
            return cls._instances[cls]
        
        def __new__(mcs, name, bases, attrs):
            """控制类创建"""
            print(f"SingletonMeta 创建类: {name}")
            
            # 添加实例计数器 / Add instance counter
            attrs['_instance_count'] = 0
            
            return super().__new__(mcs, name, bases, attrs)
    
    class DatabaseConnection(metaclass=SingletonMeta):
        """数据库连接类 - 单例模式"""
        
        def __init__(self, host="localhost", port=5432):
            if not hasattr(self, 'initialized'):
                print(f"初始化数据库连接: {host}:{port}")
                self.host = host
                self.port = port
                self.initialized = True
                DatabaseConnection._instance_count += 1
        
        def connect(self):
            return f"连接到 {self.host}:{self.port}"
        
        def __repr__(self):
            return f"DatabaseConnection({self.host}:{self.port})"
    
    print("1. 测试单例模式:")
    
    # 第一次创建 / First creation
    db1 = DatabaseConnection("localhost", 5432)
    print(f"db1: {db1}")
    
    # 第二次创建 / Second creation
    db2 = DatabaseConnection("remote", 3306)  # 参数被忽略
    print(f"db2: {db2}")
    
    print(f"db1 is db2: {db1 is db2}")
    print(f"实例计数: {DatabaseConnection._instance_count}")
    
    print(f"\n连接测试:")
    print(f"db1.connect(): {db1.connect()}")
    print(f"db2.connect(): {db2.connect()}")

def attribute_validation_metaclass():
    """属性验证元类 / Attribute Validation Metaclass"""
    print("\n=== 属性验证元类 Attribute Validation Metaclass ===")
    
    class ValidatedMeta(type):
        """属性验证元类"""
        
        def __new__(mcs, name, bases, attrs):
            # 收集验证规则 / Collect validation rules
            validators = {}
            
            for key, value in list(attrs.items()):
                if key.startswith('validate_'):
                    field_name = key[9:]  # 移除 'validate_' 前缀
                    validators[field_name] = value
                    del attrs[key]  # 从类属性中移除验证器
            
            # 添加验证器字典 / Add validators dictionary
            attrs['_validators'] = validators
            
            # 重写 __setattr__ 进行验证 / Override __setattr__ for validation
            original_setattr = attrs.get('__setattr__', None)
            
            def validated_setattr(self, name, value):
                # 执行验证 / Perform validation
                if name in self._validators:
                    validator = self._validators[name]
                    if not validator(value):
                        raise ValueError(f"验证失败: {name} = {value}")
                
                # 调用原始的 __setattr__ 或默认行为
                if original_setattr:
                    original_setattr(self, name, value)
                else:
                    super(type(self), self).__setattr__(name, value)
            
            attrs['__setattr__'] = validated_setattr
            
            return super().__new__(mcs, name, bases, attrs)
    
    class Person(metaclass=ValidatedMeta):
        """带验证的Person类"""
        
        def __init__(self, name, age, email):
            self.name = name
            self.age = age
            self.email = email
        
        # 验证规则 / Validation rules
        def validate_name(value):
            return isinstance(value, str) and len(value) > 0
        
        def validate_age(value):
            return isinstance(value, int) and 0 <= value <= 150
        
        def validate_email(value):
            return isinstance(value, str) and '@' in value
        
        def __repr__(self):
            return f"Person('{self.name}', {self.age}, '{self.email}')"
    
    print("1. 创建有效的Person实例:")
    try:
        person = Person("Alice", 30, "<EMAIL>")
        print(f"成功创建: {person}")
    except ValueError as e:
        print(f"创建失败: {e}")
    
    print("\n2. 测试属性验证:")
    
    # 有效的修改 / Valid modification
    try:
        person.age = 31
        print(f"成功修改年龄: {person}")
    except ValueError as e:
        print(f"修改失败: {e}")
    
    # 无效的修改 / Invalid modification
    try:
        person.age = -5
        print(f"修改年龄成功: {person}")
    except ValueError as e:
        print(f"修改失败 (预期): {e}")
    
    try:
        person.email = "invalid-email"
        print(f"修改邮箱成功: {person}")
    except ValueError as e:
        print(f"修改失败 (预期): {e}")

def orm_like_metaclass():
    """ORM风格的元类 / ORM-like Metaclass"""
    print("\n=== ORM风格的元类 ORM-like Metaclass ===")
    
    class Field:
        """字段描述符"""
        
        def __init__(self, field_type, default=None):
            self.field_type = field_type
            self.default = default
            self.name = None
        
        def __set_name__(self, owner, name):
            self.name = name
        
        def __get__(self, obj, objtype=None):
            if obj is None:
                return self
            return obj.__dict__.get(self.name, self.default)
        
        def __set__(self, obj, value):
            if not isinstance(value, self.field_type):
                raise TypeError(f"{self.name} 必须是 {self.field_type.__name__} 类型")
            obj.__dict__[self.name] = value
    
    class ModelMeta(type):
        """模型元类"""
        
        def __new__(mcs, name, bases, attrs):
            # 收集字段 / Collect fields
            fields = {}
            
            for key, value in attrs.items():
                if isinstance(value, Field):
                    fields[key] = value
            
            attrs['_fields'] = fields
            
            # 添加便利方法 / Add convenience methods
            def to_dict(self):
                return {name: getattr(self, name) for name in self._fields}
            
            def from_dict(cls, data):
                instance = cls.__new__(cls)
                for name, value in data.items():
                    if name in cls._fields:
                        setattr(instance, name, value)
                return instance
            
            attrs['to_dict'] = to_dict
            attrs['from_dict'] = classmethod(from_dict)
            
            return super().__new__(mcs, name, bases, attrs)
    
    class User(metaclass=ModelMeta):
        """用户模型"""
        
        name = Field(str, "")
        age = Field(int, 0)
        email = Field(str, "")
        
        def __init__(self, name="", age=0, email=""):
            self.name = name
            self.age = age
            self.email = email
        
        def __repr__(self):
            return f"User(name='{self.name}', age={self.age}, email='{self.email}')"
    
    print("1. 创建用户模型:")
    user = User("Bob", 25, "<EMAIL>")
    print(f"用户: {user}")
    print(f"字段列表: {list(user._fields.keys())}")
    
    print("\n2. 类型验证:")
    try:
        user.age = "invalid"  # 应该失败
    except TypeError as e:
        print(f"类型验证成功: {e}")
    
    print("\n3. 字典转换:")
    user_dict = user.to_dict()
    print(f"转为字典: {user_dict}")
    
    new_user = User.from_dict({"name": "Charlie", "age": 35, "email": "<EMAIL>"})
    print(f"从字典创建: {new_user}")

def metaclass_best_practices():
    """元类最佳实践 / Metaclass Best Practices"""
    print("\n=== 元类最佳实践 Metaclass Best Practices ===")
    
    print("元类使用建议:")
    print("1. 元类是高级特性，大多数情况下不需要使用")
    print("2. 考虑使用装饰器、描述符或类装饰器替代")
    print("3. 元类主要用于框架和库的开发")
    print("4. 如果你不确定是否需要元类，那么你可能不需要")
    
    print("\n常见的元类使用场景:")
    print("- ORM框架 (如Django的Model)")
    print("- 单例模式")
    print("- 属性验证")
    print("- API包装器")
    print("- 插件系统")
    
    print("\n替代方案:")
    print("- 类装饰器: 用于修改类")
    print("- 描述符: 用于属性控制")
    print("- __init_subclass__: 用于子类定制")
    
    # 演示 __init_subclass__ 替代元类 / Demonstrate __init_subclass__ as metaclass alternative
    class RegisteredBase:
        """使用 __init_subclass__ 的基类"""
        
        _registry = {}
        
        def __init_subclass__(cls, **kwargs):
            super().__init_subclass__(**kwargs)
            print(f"注册类: {cls.__name__}")
            cls._registry[cls.__name__] = cls
        
        @classmethod
        def get_registered_classes(cls):
            return cls._registry
    
    class ServiceA(RegisteredBase):
        pass
    
    class ServiceB(RegisteredBase):
        pass
    
    print(f"\n注册的类: {RegisteredBase.get_registered_classes()}")

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python元类学习示例")
    print("Python Metaclass Learning Examples")
    print("=" * 50)

    understanding_metaclasses()
    creating_classes_dynamically()
    simple_metaclass_example()
    metaclass_with_class()
    attribute_validation_metaclass()
    orm_like_metaclass()
    metaclass_best_practices()

    print("\n" + "=" * 50)
    print("元类学习完成！Metaclass learning completed!")
