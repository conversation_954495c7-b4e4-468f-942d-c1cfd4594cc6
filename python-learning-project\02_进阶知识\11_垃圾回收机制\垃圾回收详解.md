# Python垃圾回收机制详解
# Python Garbage Collection Mechanism Detailed Explanation

## 什么是垃圾回收？ / What is Garbage Collection?

垃圾回收（Garbage Collection，简称GC）是自动内存管理的一种形式，它自动释放程序不再使用的内存空间。Python使用多种垃圾回收机制来管理内存。

Garbage Collection (GC) is a form of automatic memory management that automatically frees memory space no longer used by the program. Python uses multiple garbage collection mechanisms to manage memory.

## Python的内存管理机制 / Python's Memory Management Mechanisms

### 1. 引用计数 (Reference Counting)

引用计数是Python的主要内存管理机制。每个对象都有一个引用计数器，记录有多少个引用指向该对象。

```python
import sys

def demonstrate_reference_counting():
    """演示引用计数机制"""
    
    print("=== 引用计数演示 ===")
    
    # 创建对象
    a = [1, 2, 3]
    print(f"创建列表后的引用计数: {sys.getrefcount(a)}")
    
    # 增加引用
    b = a
    print(f"赋值给b后的引用计数: {sys.getrefcount(a)}")
    
    c = a
    print(f"赋值给c后的引用计数: {sys.getrefcount(a)}")
    
    # 减少引用
    del b
    print(f"删除b后的引用计数: {sys.getrefcount(a)}")
    
    del c
    print(f"删除c后的引用计数: {sys.getrefcount(a)}")
    
    # 注意：sys.getrefcount()本身也会增加一个临时引用

demonstrate_reference_counting()
```

### 2. 引用计数的优缺点 / Pros and Cons of Reference Counting

**优点 / Advantages:**
- 实时回收：对象引用计数为0时立即回收
- 简单高效：开销小，实现简单
- 确定性：回收时机可预测

**缺点 / Disadvantages:**
- 循环引用问题：无法处理循环引用
- 额外开销：每次引用变化都需要更新计数
- 线程安全：需要额外的同步机制

### 3. 循环引用问题 / Circular Reference Problem

```python
import gc
import weakref

def demonstrate_circular_reference():
    """演示循环引用问题"""
    
    print("=== 循环引用演示 ===")
    
    class Node:
        def __init__(self, value):
            self.value = value
            self.parent = None
            self.children = []
        
        def add_child(self, child):
            child.parent = self
            self.children.append(child)
        
        def __del__(self):
            print(f"Node {self.value} 被回收")
    
    print("1. 创建循环引用:")
    
    # 创建节点
    root = Node("root")
    child1 = Node("child1")
    child2 = Node("child2")
    
    # 创建循环引用
    root.add_child(child1)
    root.add_child(child2)
    child1.add_child(Node("grandchild"))
    
    print("节点创建完成，存在循环引用")
    
    # 删除根引用
    del root, child1, child2
    
    print("删除变量引用后...")
    
    # 强制垃圾回收
    print("执行垃圾回收前:")
    collected = gc.collect()
    print(f"垃圾回收清理了 {collected} 个对象")

demonstrate_circular_reference()
```

## Python的垃圾回收器 / Python's Garbage Collector

### 1. 分代垃圾回收 / Generational Garbage Collection

Python使用分代垃圾回收算法，基于"大多数对象都是短命的"这一观察。

```python
import gc

def demonstrate_generational_gc():
    """演示分代垃圾回收"""
    
    print("=== 分代垃圾回收演示 ===")
    
    # 获取垃圾回收统计信息
    print("1. 垃圾回收统计信息:")
    stats = gc.get_stats()
    for i, stat in enumerate(stats):
        print(f"  第{i}代: {stat}")
    
    # 获取垃圾回收计数
    print("\n2. 各代对象计数:")
    counts = gc.get_count()
    print(f"  第0代: {counts[0]} 个对象")
    print(f"  第1代: {counts[1]} 个对象") 
    print(f"  第2代: {counts[2]} 个对象")
    
    # 获取垃圾回收阈值
    print("\n3. 垃圾回收阈值:")
    thresholds = gc.get_threshold()
    print(f"  第0代阈值: {thresholds[0]}")
    print(f"  第1代阈值: {thresholds[1]}")
    print(f"  第2代阈值: {thresholds[2]}")
    
    # 创建大量对象测试垃圾回收
    print("\n4. 创建大量对象:")
    objects = []
    for i in range(1000):
        obj = {"id": i, "data": list(range(10))}
        objects.append(obj)
    
    print("对象创建后的计数:")
    counts = gc.get_count()
    print(f"  第0代: {counts[0]} 个对象")
    print(f"  第1代: {counts[1]} 个对象")
    print(f"  第2代: {counts[2]} 个对象")
    
    # 手动触发垃圾回收
    print("\n5. 手动垃圾回收:")
    collected = gc.collect()
    print(f"回收了 {collected} 个对象")
    
    counts = gc.get_count()
    print(f"回收后计数:")
    print(f"  第0代: {counts[0]} 个对象")
    print(f"  第1代: {counts[1]} 个对象")
    print(f"  第2代: {counts[2]} 个对象")

demonstrate_generational_gc()
```

### 2. 垃圾回收的触发条件 / GC Trigger Conditions

垃圾回收在以下情况下触发：

1. **分配计数达到阈值** - 新对象分配数量达到设定阈值
2. **手动调用** - 调用`gc.collect()`
3. **内存压力** - 系统内存不足时
4. **程序退出** - 程序结束时清理所有对象

### 3. 垃圾回收的配置 / GC Configuration

```python
import gc

def configure_garbage_collection():
    """配置垃圾回收"""
    
    print("=== 垃圾回收配置 ===")
    
    # 获取当前配置
    print("1. 当前配置:")
    print(f"  垃圾回收是否启用: {gc.isenabled()}")
    print(f"  当前阈值: {gc.get_threshold()}")
    
    # 修改阈值
    print("\n2. 修改阈值:")
    original_threshold = gc.get_threshold()
    gc.set_threshold(500, 10, 10)  # 降低阈值，更频繁回收
    print(f"  新阈值: {gc.get_threshold()}")
    
    # 禁用垃圾回收
    print("\n3. 禁用垃圾回收:")
    gc.disable()
    print(f"  垃圾回收是否启用: {gc.isenabled()}")
    
    # 重新启用垃圾回收
    print("\n4. 重新启用垃圾回收:")
    gc.enable()
    print(f"  垃圾回收是否启用: {gc.isenabled()}")
    
    # 恢复原始阈值
    gc.set_threshold(*original_threshold)
    print(f"  恢复原始阈值: {gc.get_threshold()}")

configure_garbage_collection()
```

## 内存泄漏的检测和预防 / Memory Leak Detection and Prevention

### 1. 检测内存泄漏 / Detecting Memory Leaks

```python
import gc
import sys
from collections import defaultdict

def detect_memory_leaks():
    """检测内存泄漏"""
    
    print("=== 内存泄漏检测 ===")
    
    def get_object_counts():
        """获取对象类型计数"""
        counts = defaultdict(int)
        for obj in gc.get_objects():
            counts[type(obj).__name__] += 1
        return counts
    
    print("1. 初始对象计数:")
    initial_counts = get_object_counts()
    for obj_type, count in sorted(initial_counts.items())[:10]:
        print(f"  {obj_type}: {count}")
    
    # 创建可能泄漏的对象
    print("\n2. 创建对象:")
    leaked_objects = []
    for i in range(1000):
        obj = {
            'id': i,
            'data': list(range(100)),
            'circular_ref': None
        }
        obj['circular_ref'] = obj  # 创建循环引用
        leaked_objects.append(obj)
    
    print("对象创建完成")
    
    # 检查对象计数变化
    print("\n3. 对象计数变化:")
    current_counts = get_object_counts()
    for obj_type in ['dict', 'list', 'int']:
        initial = initial_counts.get(obj_type, 0)
        current = current_counts.get(obj_type, 0)
        print(f"  {obj_type}: {initial} -> {current} (增加 {current - initial})")
    
    # 删除引用但保持循环引用
    print("\n4. 删除直接引用:")
    del leaked_objects
    
    # 检查是否还有未回收的对象
    print("\n5. 垃圾回收前:")
    before_gc_counts = get_object_counts()
    
    collected = gc.collect()
    print(f"垃圾回收清理了 {collected} 个对象")
    
    print("\n6. 垃圾回收后:")
    after_gc_counts = get_object_counts()
    for obj_type in ['dict', 'list', 'int']:
        before = before_gc_counts.get(obj_type, 0)
        after = after_gc_counts.get(obj_type, 0)
        if before != after:
            print(f"  {obj_type}: {before} -> {after} (减少 {before - after})")

detect_memory_leaks()
```

### 2. 预防内存泄漏的最佳实践 / Best Practices for Preventing Memory Leaks

```python
import weakref
from contextlib import contextmanager

def memory_leak_prevention():
    """内存泄漏预防示例"""
    
    print("=== 内存泄漏预防 ===")
    
    # 1. 使用弱引用避免循环引用
    print("1. 使用弱引用:")
    
    class Parent:
        def __init__(self, name):
            self.name = name
            self.children = []
        
        def add_child(self, child):
            self.children.append(child)
            child.parent = weakref.ref(self)  # 使用弱引用
        
        def __del__(self):
            print(f"Parent {self.name} 被回收")
    
    class Child:
        def __init__(self, name):
            self.name = name
            self.parent = None
        
        def get_parent(self):
            if self.parent is not None:
                return self.parent()  # 调用弱引用
            return None
        
        def __del__(self):
            print(f"Child {self.name} 被回收")
    
    # 创建父子关系
    parent = Parent("父节点")
    child = Child("子节点")
    parent.add_child(child)
    
    print(f"子节点的父节点: {child.get_parent().name}")
    
    # 删除引用
    del parent, child
    print("删除引用后，对象应该被自动回收")
    
    # 2. 使用上下文管理器
    print("\n2. 使用上下文管理器:")
    
    @contextmanager
    def resource_manager(resource_name):
        """资源管理器"""
        print(f"获取资源: {resource_name}")
        resource = {"name": resource_name, "data": list(range(1000))}
        try:
            yield resource
        finally:
            print(f"释放资源: {resource_name}")
            resource.clear()  # 清理资源
    
    # 使用资源管理器
    with resource_manager("数据库连接") as resource:
        print(f"使用资源: {resource['name']}")
    
    print("资源已自动释放")
    
    # 3. 及时清理引用
    print("\n3. 及时清理引用:")
    
    def process_large_data():
        """处理大量数据"""
        large_data = list(range(100000))
        
        # 处理数据
        result = sum(large_data)
        
        # 及时清理不需要的数据
        del large_data
        
        return result
    
    result = process_large_data()
    print(f"处理结果: {result}")

memory_leak_prevention()
```

## 性能优化建议 / Performance Optimization Tips

### 1. 对象池模式 / Object Pool Pattern

```python
class ObjectPool:
    """对象池实现"""
    
    def __init__(self, create_func, reset_func=None, max_size=10):
        self.create_func = create_func
        self.reset_func = reset_func
        self.max_size = max_size
        self.pool = []
    
    def acquire(self):
        """获取对象"""
        if self.pool:
            obj = self.pool.pop()
            if self.reset_func:
                self.reset_func(obj)
            return obj
        else:
            return self.create_func()
    
    def release(self, obj):
        """释放对象"""
        if len(self.pool) < self.max_size:
            self.pool.append(obj)

def demonstrate_object_pool():
    """演示对象池"""
    
    print("=== 对象池演示 ===")
    
    def create_list():
        """创建列表对象"""
        print("创建新的列表对象")
        return []
    
    def reset_list(lst):
        """重置列表对象"""
        lst.clear()
    
    # 创建对象池
    list_pool = ObjectPool(create_list, reset_list, max_size=3)
    
    # 使用对象池
    print("1. 获取对象:")
    obj1 = list_pool.acquire()  # 创建新对象
    obj2 = list_pool.acquire()  # 创建新对象
    
    obj1.extend([1, 2, 3])
    obj2.extend([4, 5, 6])
    
    print(f"obj1: {obj1}")
    print(f"obj2: {obj2}")
    
    print("\n2. 释放对象:")
    list_pool.release(obj1)
    list_pool.release(obj2)
    
    print("\n3. 重新获取对象:")
    obj3 = list_pool.acquire()  # 重用对象
    obj4 = list_pool.acquire()  # 重用对象
    
    print(f"obj3 (重用): {obj3}")
    print(f"obj4 (重用): {obj4}")

demonstrate_object_pool()
```

### 2. 内存使用监控 / Memory Usage Monitoring

```python
import psutil
import os

def monitor_memory_usage():
    """监控内存使用"""
    
    print("=== 内存使用监控 ===")
    
    def get_memory_info():
        """获取内存信息"""
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        return {
            'rss': memory_info.rss / 1024 / 1024,  # MB
            'vms': memory_info.vms / 1024 / 1024,  # MB
        }
    
    print("1. 初始内存使用:")
    initial_memory = get_memory_info()
    print(f"  RSS: {initial_memory['rss']:.2f} MB")
    print(f"  VMS: {initial_memory['vms']:.2f} MB")
    
    print("\n2. 创建大量对象:")
    large_objects = []
    for i in range(10000):
        obj = {
            'id': i,
            'data': list(range(100)),
            'text': f"Object {i}" * 10
        }
        large_objects.append(obj)
    
    after_creation_memory = get_memory_info()
    print(f"  RSS: {after_creation_memory['rss']:.2f} MB")
    print(f"  VMS: {after_creation_memory['vms']:.2f} MB")
    print(f"  RSS增加: {after_creation_memory['rss'] - initial_memory['rss']:.2f} MB")
    
    print("\n3. 删除对象:")
    del large_objects
    
    # 强制垃圾回收
    import gc
    collected = gc.collect()
    print(f"垃圾回收清理了 {collected} 个对象")
    
    after_deletion_memory = get_memory_info()
    print(f"  RSS: {after_deletion_memory['rss']:.2f} MB")
    print(f"  VMS: {after_deletion_memory['vms']:.2f} MB")
    print(f"  RSS减少: {after_creation_memory['rss'] - after_deletion_memory['rss']:.2f} MB")

# 注意：需要安装psutil库
# monitor_memory_usage()
```

## 总结 / Summary

Python的垃圾回收机制包括：

1. **引用计数** - 主要机制，实时回收
2. **循环垃圾回收** - 处理循环引用
3. **分代回收** - 基于对象生命周期的优化

**最佳实践：**
- 避免不必要的循环引用
- 使用弱引用处理反向引用
- 及时清理大对象的引用
- 使用上下文管理器管理资源
- 监控内存使用情况
- 在必要时手动触发垃圾回收

理解垃圾回收机制有助于编写更高效、更稳定的Python程序。

---

*注意：某些代码示例可能需要安装额外的库（如psutil）才能运行。*
