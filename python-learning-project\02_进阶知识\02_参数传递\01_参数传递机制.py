#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
参数传递 (Parameter Passing) - Python参数传递机制
Parameter Passing - Python Parameter Passing Mechanism

理解Python的参数传递机制对于编写正确和高效的函数至关重要。
Understanding Python's parameter passing mechanism is crucial for writing correct and efficient functions.
"""

import copy
from typing import List, Dict, Any, Optional

def pass_by_object_reference():
    """按对象引用传递 / Pass by Object Reference"""
    print("=== 按对象引用传递 Pass by Object Reference ===")
    
    print("1. Python参数传递的本质:")
    print("   - Python既不是按值传递，也不是按引用传递")
    print("   - Python是按对象引用传递 (pass by object reference)")
    print("   - 传递的是对象的引用，而不是对象本身")
    
    print("\n2. 不可变对象的传递:")
    
    def modify_immutable(x):
        """尝试修改不可变对象"""
        print(f"  函数内部 - 接收到的参数: {x}, id: {id(x)}")
        x = x + 10  # 创建新对象
        print(f"  函数内部 - 修改后的参数: {x}, id: {id(x)}")
        return x
    
    original_num = 5
    print(f"原始数字: {original_num}, id: {id(original_num)}")
    
    result = modify_immutable(original_num)
    print(f"函数返回值: {result}")
    print(f"原始数字 (未变): {original_num}, id: {id(original_num)}")
    
    print("\n3. 可变对象的传递:")
    
    def modify_mutable(lst):
        """修改可变对象"""
        print(f"  函数内部 - 接收到的列表: {lst}, id: {id(lst)}")
        lst.append(4)  # 修改原对象
        print(f"  函数内部 - 修改后的列表: {lst}, id: {id(lst)}")
        return lst
    
    original_list = [1, 2, 3]
    print(f"原始列表: {original_list}, id: {id(original_list)}")
    
    result = modify_mutable(original_list)
    print(f"函数返回值: {result}")
    print(f"原始列表 (已变): {original_list}, id: {id(original_list)}")
    print(f"result is original_list: {result is original_list}")

def immutable_vs_mutable_parameters():
    """不可变 vs 可变参数 / Immutable vs Mutable Parameters"""
    print("\n=== 不可变 vs 可变参数 Immutable vs Mutable Parameters ===")
    
    print("1. 不可变对象作为参数:")
    
    def process_immutable_types(num, text, tup):
        """处理不可变类型"""
        print(f"  原始值: num={num}, text='{text}', tup={tup}")
        
        # 尝试"修改" - 实际上创建新对象
        num += 1
        text += " modified"
        tup += (4,)
        
        print(f"  函数内修改后: num={num}, text='{text}', tup={tup}")
        return num, text, tup
    
    original_num = 10
    original_text = "hello"
    original_tuple = (1, 2, 3)
    
    print(f"调用前: num={original_num}, text='{original_text}', tup={original_tuple}")
    
    result = process_immutable_types(original_num, original_text, original_tuple)
    
    print(f"调用后: num={original_num}, text='{original_text}', tup={original_tuple}")
    print(f"返回值: {result}")
    
    print("\n2. 可变对象作为参数:")
    
    def process_mutable_types(lst, dct, st):
        """处理可变类型"""
        print(f"  原始值: lst={lst}, dct={dct}, set={st}")
        
        # 修改原对象
        lst.append("new_item")
        dct["new_key"] = "new_value"
        st.add("new_element")
        
        print(f"  函数内修改后: lst={lst}, dct={dct}, set={st}")
        return lst, dct, st
    
    original_list = [1, 2, 3]
    original_dict = {"key": "value"}
    original_set = {1, 2, 3}
    
    print(f"调用前: lst={original_list}, dct={original_dict}, set={original_set}")
    
    result = process_mutable_types(original_list, original_dict, original_set)
    
    print(f"调用后: lst={original_list}, dct={original_dict}, set={original_set}")
    print(f"返回值与原对象相同: {result[0] is original_list}")

def parameter_reassignment():
    """参数重新赋值 / Parameter Reassignment"""
    print("\n=== 参数重新赋值 Parameter Reassignment ===")
    
    print("1. 重新赋值不可变对象:")
    
    def reassign_immutable(x):
        """重新赋值不可变参数"""
        print(f"  函数开始: x={x}, id={id(x)}")
        x = "completely new value"  # 重新赋值
        print(f"  重新赋值后: x={x}, id={id(x)}")
        return x
    
    original = "original value"
    print(f"调用前: original='{original}', id={id(original)}")
    
    result = reassign_immutable(original)
    print(f"调用后: original='{original}', id={id(original)}")
    print(f"返回值: '{result}'")
    
    print("\n2. 重新赋值可变对象:")
    
    def reassign_mutable(lst):
        """重新赋值可变参数"""
        print(f"  函数开始: lst={lst}, id={id(lst)}")
        lst.append("modified")  # 修改原对象
        print(f"  修改后: lst={lst}, id={id(lst)}")
        
        lst = ["completely", "new", "list"]  # 重新赋值
        print(f"  重新赋值后: lst={lst}, id={id(lst)}")
        return lst
    
    original_list = [1, 2, 3]
    print(f"调用前: original_list={original_list}, id={id(original_list)}")
    
    result = reassign_mutable(original_list)
    print(f"调用后: original_list={original_list}, id={id(original_list)}")
    print(f"返回值: {result}")
    print(f"返回值是原对象: {result is original_list}")

def avoiding_side_effects():
    """避免副作用 / Avoiding Side Effects"""
    print("\n=== 避免副作用 Avoiding Side Effects ===")
    
    print("1. 问题: 意外修改可变参数")
    
    def bad_function(data_list):
        """不好的函数 - 修改了输入参数"""
        data_list.sort()  # 修改原列表!
        data_list.append("processed")
        return data_list
    
    original_data = [3, 1, 4, 1, 5]
    print(f"原始数据: {original_data}")
    
    processed = bad_function(original_data)
    print(f"处理后数据: {processed}")
    print(f"原始数据被修改了: {original_data}")  # 意外的副作用!
    
    print("\n2. 解决方案1: 创建副本")
    
    def good_function_copy(data_list):
        """好的函数 - 使用副本"""
        data_copy = data_list.copy()  # 或 list(data_list)
        data_copy.sort()
        data_copy.append("processed")
        return data_copy
    
    original_data2 = [3, 1, 4, 1, 5]
    print(f"原始数据: {original_data2}")
    
    processed2 = good_function_copy(original_data2)
    print(f"处理后数据: {processed2}")
    print(f"原始数据未变: {original_data2}")
    
    print("\n3. 解决方案2: 返回新对象")
    
    def good_function_new(data_list):
        """好的函数 - 返回新对象"""
        sorted_data = sorted(data_list)  # 返回新列表
        return sorted_data + ["processed"]
    
    original_data3 = [3, 1, 4, 1, 5]
    print(f"原始数据: {original_data3}")
    
    processed3 = good_function_new(original_data3)
    print(f"处理后数据: {processed3}")
    print(f"原始数据未变: {original_data3}")
    
    print("\n4. 解决方案3: 深复制 (对于嵌套结构)")
    
    def process_nested_data(data):
        """处理嵌套数据结构"""
        data_copy = copy.deepcopy(data)  # 深复制
        for sublist in data_copy:
            sublist.sort()
        return data_copy
    
    nested_data = [[3, 1, 4], [2, 7, 1], [8, 5, 9]]
    print(f"原始嵌套数据: {nested_data}")
    
    processed_nested = process_nested_data(nested_data)
    print(f"处理后嵌套数据: {processed_nested}")
    print(f"原始嵌套数据未变: {nested_data}")

def default_parameter_pitfalls():
    """默认参数陷阱 / Default Parameter Pitfalls"""
    print("\n=== 默认参数陷阱 Default Parameter Pitfalls ===")
    
    print("1. 可变默认参数的问题:")
    
    def append_to_list(item, target_list=[]):
        """危险: 可变默认参数"""
        target_list.append(item)
        return target_list
    
    print("多次调用同一函数:")
    result1 = append_to_list("first")
    print(f"第一次调用: {result1}")
    
    result2 = append_to_list("second")
    print(f"第二次调用: {result2}")  # 包含第一次的结果!
    
    result3 = append_to_list("third")
    print(f"第三次调用: {result3}")  # 包含所有之前的结果!
    
    print(f"所有结果都是同一个对象: {result1 is result2 is result3}")
    
    print("\n2. 正确的做法:")
    
    def append_to_list_correct(item, target_list=None):
        """正确: 使用None作为默认值"""
        if target_list is None:
            target_list = []  # 每次创建新列表
        target_list.append(item)
        return target_list
    
    print("使用正确的方法:")
    result1 = append_to_list_correct("first")
    print(f"第一次调用: {result1}")
    
    result2 = append_to_list_correct("second")
    print(f"第二次调用: {result2}")
    
    result3 = append_to_list_correct("third")
    print(f"第三次调用: {result3}")
    
    print(f"结果是不同的对象: {result1 is result2}")
    
    print("\n3. 查看默认参数的状态:")
    
    def show_default_state(item, target_list=[]):
        """显示默认参数的状态"""
        print(f"  默认列表的id: {id(target_list)}")
        print(f"  默认列表的内容: {target_list}")
        target_list.append(item)
        return target_list
    
    print("观察默认参数的变化:")
    show_default_state("item1")
    show_default_state("item2")
    show_default_state("item3")

def parameter_passing_patterns():
    """参数传递模式 / Parameter Passing Patterns"""
    print("\n=== 参数传递模式 Parameter Passing Patterns ===")
    
    print("1. 输入参数模式 (只读):")
    
    def calculate_statistics(numbers: List[float]) -> Dict[str, float]:
        """计算统计信息 - 不修改输入"""
        if not numbers:
            return {}
        
        return {
            "count": len(numbers),
            "sum": sum(numbers),
            "mean": sum(numbers) / len(numbers),
            "min": min(numbers),
            "max": max(numbers)
        }
    
    data = [1.5, 2.3, 3.7, 4.1, 5.9]
    stats = calculate_statistics(data)
    print(f"数据: {data}")
    print(f"统计信息: {stats}")
    print(f"原数据未变: {data}")
    
    print("\n2. 输入输出参数模式 (修改输入):")
    
    def normalize_list(numbers: List[float]) -> None:
        """就地标准化列表 - 修改输入参数"""
        if not numbers:
            return
        
        mean = sum(numbers) / len(numbers)
        std = (sum((x - mean) ** 2 for x in numbers) / len(numbers)) ** 0.5
        
        if std == 0:
            return
        
        for i in range(len(numbers)):
            numbers[i] = (numbers[i] - mean) / std
    
    data2 = [1.0, 2.0, 3.0, 4.0, 5.0]
    print(f"标准化前: {data2}")
    normalize_list(data2)
    print(f"标准化后: {[round(x, 3) for x in data2]}")
    
    print("\n3. 工厂函数模式 (返回新对象):")
    
    def create_processed_data(raw_data: List[Any], 
                            filter_func=None, 
                            transform_func=None) -> List[Any]:
        """创建处理后的数据 - 返回新对象"""
        result = raw_data.copy()  # 创建副本
        
        if filter_func:
            result = [item for item in result if filter_func(item)]
        
        if transform_func:
            result = [transform_func(item) for item in result]
        
        return result
    
    raw_data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    
    # 过滤偶数并平方
    processed = create_processed_data(
        raw_data,
        filter_func=lambda x: x % 2 == 0,
        transform_func=lambda x: x ** 2
    )
    
    print(f"原始数据: {raw_data}")
    print(f"处理后数据: {processed}")

def advanced_parameter_techniques():
    """高级参数技巧 / Advanced Parameter Techniques"""
    print("\n=== 高级参数技巧 Advanced Parameter Techniques ===")
    
    print("1. 使用*args和**kwargs避免副作用:")
    
    def safe_function(*args, **kwargs):
        """安全函数 - 处理任意参数"""
        # 创建参数的副本
        safe_args = []
        for arg in args:
            if hasattr(arg, 'copy'):
                safe_args.append(arg.copy())
            elif isinstance(arg, (list, dict, set)):
                safe_args.append(copy.deepcopy(arg))
            else:
                safe_args.append(arg)
        
        safe_kwargs = copy.deepcopy(kwargs)
        
        print(f"  安全处理参数: args={safe_args}, kwargs={safe_kwargs}")
        
        # 修改副本
        if safe_args and isinstance(safe_args[0], list):
            safe_args[0].append("modified")
        
        return safe_args, safe_kwargs
    
    original_list = [1, 2, 3]
    original_dict = {"key": "value"}
    
    result = safe_function(original_list, data=original_dict)
    print(f"原始列表: {original_list}")
    print(f"原始字典: {original_dict}")
    
    print("\n2. 参数验证和转换:")
    
    def robust_function(data, *, copy_data=True, validate=True):
        """健壮的函数 - 参数验证和可选复制"""
        if validate:
            if not isinstance(data, (list, tuple)):
                raise TypeError("data必须是列表或元组")
            
            if not all(isinstance(x, (int, float)) for x in data):
                raise ValueError("data必须包含数字")
        
        # 根据参数决定是否复制
        working_data = list(data) if copy_data else data
        
        # 处理数据
        working_data.sort()
        
        return working_data
    
    test_data = [3, 1, 4, 1, 5]
    
    # 使用复制 (默认)
    result1 = robust_function(test_data)
    print(f"使用复制 - 原数据: {test_data}, 结果: {result1}")
    
    # 不使用复制
    test_data2 = [3, 1, 4, 1, 5]
    result2 = robust_function(test_data2, copy_data=False)
    print(f"不使用复制 - 原数据: {test_data2}, 结果: {result2}")

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python参数传递机制学习示例")
    print("Python Parameter Passing Mechanism Learning Examples")
    print("=" * 60)
    
    pass_by_object_reference()
    immutable_vs_mutable_parameters()
    parameter_reassignment()
    avoiding_side_effects()
    default_parameter_pitfalls()
    parameter_passing_patterns()
    advanced_parameter_techniques()
    
    print("\n" + "=" * 60)
    print("参数传递机制学习完成！Parameter passing mechanism learning completed!")
