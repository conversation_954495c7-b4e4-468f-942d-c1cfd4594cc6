#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集合 (Set) - Python无序不重复元素集合
Sets - Python Unordered Collection of Unique Elements

集合是Python中用于存储无序、不重复元素的数据结构，支持数学集合运算。
Sets are data structures in Python for storing unordered, unique elements, supporting mathematical set operations.
"""

def basic_set_operations():
    """基本集合操作 / Basic Set Operations"""
    print("=== 基本集合操作 Basic Set Operations ===")
    
    # 创建集合 / Creating sets
    empty_set = set()  # 空集合(注意不能用{}) / Empty set (note: cannot use {})
    numbers = {1, 2, 3, 4, 5}  # 数字集合 / Number set
    fruits = {"苹果", "香蕉", "橙子"}  # 水果集合 / Fruit set
    mixed = {1, "hello", 3.14}  # 混合类型集合 / Mixed type set
    
    print(f"空集合: {empty_set}")
    print(f"数字集合: {numbers}")
    print(f"水果集合: {fruits}")
    print(f"混合集合: {mixed}")
    
    # 从列表创建集合(自动去重) / Create set from list (automatic deduplication)
    list_with_duplicates = [1, 2, 2, 3, 3, 3, 4]
    unique_numbers = set(list_with_duplicates)
    print(f"原列表: {list_with_duplicates}")
    print(f"去重后集合: {unique_numbers}")
    
    # 集合长度 / Set length
    print(f"水果集合大小: {len(fruits)}")
    
    # 成员检查 / Membership testing
    print(f"苹果在集合中: {'苹果' in fruits}")
    print(f"西瓜在集合中: {'西瓜' in fruits}")

def set_modification():
    """集合修改操作 / Set Modification Operations"""
    print("\n=== 集合修改操作 Set Modification Operations ===")
    
    colors = {"红色", "绿色", "蓝色"}
    print(f"原始颜色集合: {colors}")
    
    # 添加元素 / Adding elements
    colors.add("黄色")
    print(f"添加黄色: {colors}")
    
    # 尝试添加重复元素 / Try adding duplicate element
    colors.add("红色")  # 不会有任何变化 / No change will occur
    print(f"尝试添加重复的红色: {colors}")
    
    # 批量添加 / Batch adding
    colors.update(["紫色", "橙色", "粉色"])
    print(f"批量添加颜色: {colors}")
    
    # 删除元素 / Removing elements
    colors.remove("粉色")  # 如果元素不存在会报错 / Will raise error if element doesn't exist
    print(f"删除粉色: {colors}")
    
    colors.discard("黑色")  # 如果元素不存在不会报错 / Won't raise error if element doesn't exist
    print(f"尝试删除不存在的黑色: {colors}")
    
    # 随机删除并返回元素 / Randomly remove and return element
    removed_color = colors.pop()
    print(f"随机删除的颜色: {removed_color}")
    print(f"剩余颜色: {colors}")
    
    # 清空集合 / Clear set
    temp_colors = colors.copy()
    temp_colors.clear()
    print(f"清空后的临时集合: {temp_colors}")

def set_operations():
    """集合运算 / Set Operations"""
    print("\n=== 集合运算 Set Operations ===")
    
    # 创建两个集合用于演示 / Create two sets for demonstration
    set_a = {1, 2, 3, 4, 5}
    set_b = {4, 5, 6, 7, 8}
    
    print(f"集合A: {set_a}")
    print(f"集合B: {set_b}")
    
    # 并集 / Union
    union_result = set_a | set_b  # 或者 set_a.union(set_b)
    print(f"并集 A ∪ B: {union_result}")
    
    # 交集 / Intersection
    intersection_result = set_a & set_b  # 或者 set_a.intersection(set_b)
    print(f"交集 A ∩ B: {intersection_result}")
    
    # 差集 / Difference
    difference_result = set_a - set_b  # 或者 set_a.difference(set_b)
    print(f"差集 A - B: {difference_result}")
    print(f"差集 B - A: {set_b - set_a}")
    
    # 对称差集 / Symmetric difference
    symmetric_diff = set_a ^ set_b  # 或者 set_a.symmetric_difference(set_b)
    print(f"对称差集 A △ B: {symmetric_diff}")
    
    # 子集和超集检查 / Subset and superset checking
    small_set = {2, 3}
    print(f"小集合: {small_set}")
    print(f"{small_set} 是 {set_a} 的子集: {small_set.issubset(set_a)}")
    print(f"{set_a} 是 {small_set} 的超集: {set_a.issuperset(small_set)}")
    
    # 不相交检查 / Disjoint checking
    set_c = {10, 11, 12}
    print(f"集合C: {set_c}")
    print(f"A和C不相交: {set_a.isdisjoint(set_c)}")

def frozenset_example():
    """不可变集合示例 / Frozen Set Example"""
    print("\n=== 不可变集合 Frozen Set ===")
    
    # 创建不可变集合 / Create frozen set
    frozen_numbers = frozenset([1, 2, 3, 4, 5])
    print(f"不可变集合: {frozen_numbers}")
    
    # 不可变集合可以作为字典的键 / Frozen sets can be used as dictionary keys
    set_dict = {
        frozenset([1, 2]): "小集合",
        frozenset([1, 2, 3, 4]): "大集合"
    }
    print(f"以集合为键的字典: {set_dict}")
    
    # 不可变集合支持集合运算 / Frozen sets support set operations
    frozen_a = frozenset([1, 2, 3])
    frozen_b = frozenset([2, 3, 4])
    
    print(f"不可变集合A: {frozen_a}")
    print(f"不可变集合B: {frozen_b}")
    print(f"交集: {frozen_a & frozen_b}")
    print(f"并集: {frozen_a | frozen_b}")

def practical_examples():
    """实际应用示例 / Practical Examples"""
    print("\n=== 实际应用示例 Practical Examples ===")
    
    # 1. 去重应用 / Deduplication application
    print("1. 数据去重示例:")
    user_ids = [1001, 1002, 1001, 1003, 1002, 1004, 1001]
    unique_users = set(user_ids)
    print(f"原始用户ID列表: {user_ids}")
    print(f"去重后的用户: {unique_users}")
    print(f"独特用户数量: {len(unique_users)}")
    
    # 2. 权限管理 / Permission management
    print("\n2. 权限管理示例:")
    admin_permissions = {"read", "write", "delete", "execute"}
    user_permissions = {"read", "write"}
    guest_permissions = {"read"}
    
    print(f"管理员权限: {admin_permissions}")
    print(f"用户权限: {user_permissions}")
    print(f"访客权限: {guest_permissions}")
    
    # 检查权限 / Check permissions
    def check_permission(user_perms, required_perm):
        return required_perm in user_perms
    
    print(f"用户是否有删除权限: {check_permission(user_permissions, 'delete')}")
    print(f"管理员是否有删除权限: {check_permission(admin_permissions, 'delete')}")
    
    # 权限升级 / Permission upgrade
    upgraded_user = user_permissions | {"execute"}
    print(f"升级后用户权限: {upgraded_user}")
    
    # 3. 标签系统 / Tag system
    print("\n3. 标签系统示例:")
    article1_tags = {"python", "编程", "教程", "初学者"}
    article2_tags = {"python", "高级", "性能优化"}
    article3_tags = {"java", "编程", "面向对象"}
    
    # 找出共同标签 / Find common tags
    common_tags_12 = article1_tags & article2_tags
    print(f"文章1和文章2的共同标签: {common_tags_12}")
    
    # 找出所有标签 / Find all tags
    all_tags = article1_tags | article2_tags | article3_tags
    print(f"所有文章的标签: {all_tags}")
    
    # 推荐系统：基于标签相似度 / Recommendation system: based on tag similarity
    def calculate_similarity(tags1, tags2):
        intersection = len(tags1 & tags2)
        union = len(tags1 | tags2)
        return intersection / union if union > 0 else 0
    
    similarity = calculate_similarity(article1_tags, article2_tags)
    print(f"文章1和文章2的相似度: {similarity:.2f}")
    
    # 4. 数据分析 / Data analysis
    print("\n4. 数据分析示例:")
    # 模拟两个月的活跃用户 / Simulate active users for two months
    january_users = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10}
    february_users = {5, 6, 7, 8, 9, 10, 11, 12, 13, 14}
    
    print(f"1月活跃用户: {january_users}")
    print(f"2月活跃用户: {february_users}")
    
    # 留存用户 / Retained users
    retained_users = january_users & february_users
    print(f"留存用户: {retained_users}")
    print(f"留存率: {len(retained_users) / len(january_users) * 100:.1f}%")
    
    # 新增用户 / New users
    new_users = february_users - january_users
    print(f"2月新增用户: {new_users}")
    
    # 流失用户 / Churned users
    churned_users = january_users - february_users
    print(f"流失用户: {churned_users}")

def set_performance():
    """集合性能特点 / Set Performance Characteristics"""
    print("\n=== 集合性能特点 Set Performance Characteristics ===")
    
    import time
    
    # 成员检查性能比较 / Membership testing performance comparison
    large_list = list(range(10000))
    large_set = set(range(10000))
    
    # 列表成员检查 / List membership testing
    start = time.time()
    for _ in range(1000):
        result = 9999 in large_list
    list_time = time.time() - start
    
    # 集合成员检查 / Set membership testing
    start = time.time()
    for _ in range(1000):
        result = 9999 in large_set
    set_time = time.time() - start
    
    print(f"列表成员检查1000次: {list_time:.6f}秒")
    print(f"集合成员检查1000次: {set_time:.6f}秒")
    print(f"集合比列表快 {list_time/set_time:.1f} 倍")
    
    print("\n性能建议:")
    print("- 需要频繁检查成员关系时使用集合")
    print("- 需要去重时使用集合")
    print("- 需要集合运算时使用集合")
    print("- 集合元素必须是不可变类型")

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python集合(Set)学习示例")
    print("Python Set Learning Examples")
    print("=" * 50)
    
    basic_set_operations()
    set_modification()
    set_operations()
    frozenset_example()
    practical_examples()
    set_performance()
    
    print("\n" + "=" * 50)
    print("集合学习完成！Set learning completed!")
