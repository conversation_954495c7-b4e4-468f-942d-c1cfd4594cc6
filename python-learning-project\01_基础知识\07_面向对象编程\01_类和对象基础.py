#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
面向对象编程 (Object-Oriented Programming) - Python类和对象
Object-Oriented Programming - Python Classes and Objects

面向对象编程是一种编程范式，通过类和对象来组织代码，实现数据封装、继承和多态。
Object-oriented programming is a programming paradigm that organizes code through classes and objects, implementing data encapsulation, inheritance, and polymorphism.
"""

from typing import List, Optional
from datetime import datetime
import math

def basic_class_concepts():
    """基本类概念 / Basic Class Concepts"""
    print("=== 基本类概念 Basic Class Concepts ===")
    
    # 1. 最简单的类定义 / Simplest class definition
    class Person:
        """人员类 - 最基本的类定义"""
        pass  # 空类体
    
    print("1. 创建和使用对象:")
    # 创建对象实例 / Create object instances
    person1 = Person()
    person2 = Person()
    
    print(f"person1 类型: {type(person1)}")
    print(f"person2 类型: {type(person2)}")
    print(f"是否为同一对象: {person1 is person2}")
    print(f"是否为同一类型: {type(person1) == type(person2)}")
    
    # 2. 带属性的类 / Class with attributes
    class Student:
        """学生类 - 带属性的类"""
        
        def __init__(self, name, age, student_id):
            """构造函数 / Constructor"""
            self.name = name        # 实例属性 / Instance attribute
            self.age = age
            self.student_id = student_id
            self.courses = []       # 初始化为空列表
        
        def introduce(self):
            """自我介绍方法 / Self-introduction method"""
            return f"我是{self.name}，今年{self.age}岁，学号是{self.student_id}"
        
        def add_course(self, course):
            """添加课程 / Add course"""
            self.courses.append(course)
            print(f"{self.name} 添加了课程: {course}")
        
        def list_courses(self):
            """列出所有课程 / List all courses"""
            if self.courses:
                return f"{self.name} 的课程: {', '.join(self.courses)}"
            else:
                return f"{self.name} 还没有选择任何课程"
    
    print("\n2. 使用带属性和方法的类:")
    student1 = Student("张三", 20, "2023001")
    student2 = Student("李四", 19, "2023002")
    
    print(student1.introduce())
    print(student2.introduce())
    
    student1.add_course("Python编程")
    student1.add_course("数据结构")
    student2.add_course("高等数学")
    
    print(student1.list_courses())
    print(student2.list_courses())

def class_attributes_vs_instance_attributes():
    """类属性 vs 实例属性 / Class Attributes vs Instance Attributes"""
    print("\n=== 类属性 vs 实例属性 Class Attributes vs Instance Attributes ===")
    
    class University:
        """大学类 - 演示类属性和实例属性"""
        
        # 类属性 / Class attributes
        country = "中国"
        total_universities = 0
        
        def __init__(self, name, city, founded_year):
            # 实例属性 / Instance attributes
            self.name = name
            self.city = city
            self.founded_year = founded_year
            self.students = []
            
            # 修改类属性 / Modify class attribute
            University.total_universities += 1
        
        def add_student(self, student_name):
            """添加学生 / Add student"""
            self.students.append(student_name)
        
        def get_info(self):
            """获取大学信息 / Get university information"""
            return {
                "name": self.name,
                "city": self.city,
                "country": self.country,  # 访问类属性
                "founded": self.founded_year,
                "students_count": len(self.students)
            }
        
        @classmethod
        def get_total_count(cls):
            """获取大学总数 - 类方法 / Get total count - class method"""
            return cls.total_universities
        
        @staticmethod
        def is_old_university(founded_year):
            """判断是否为老牌大学 - 静态方法 / Check if old university - static method"""
            current_year = datetime.now().year
            return (current_year - founded_year) > 100
    
    print("1. 创建大学实例:")
    tsinghua = University("清华大学", "北京", 1911)
    peking = University("北京大学", "北京", 1898)
    fudan = University("复旦大学", "上海", 1905)
    
    print(f"总大学数量: {University.get_total_count()}")
    
    # 添加学生 / Add students
    tsinghua.add_student("张三")
    tsinghua.add_student("李四")
    peking.add_student("王五")
    
    print("\n2. 大学信息:")
    for university in [tsinghua, peking, fudan]:
        info = university.get_info()
        is_old = University.is_old_university(info["founded"])
        print(f"{info['name']}: {info['city']}, 成立于{info['founded']}年, "
              f"学生数: {info['students_count']}, "
              f"{'老牌大学' if is_old else '现代大学'}")
    
    print("\n3. 类属性的共享性:")
    print(f"通过类访问: University.country = {University.country}")
    print(f"通过实例访问: tsinghua.country = {tsinghua.country}")
    
    # 修改类属性 / Modify class attribute
    University.country = "People's Republic of China"
    print(f"修改后 - tsinghua.country = {tsinghua.country}")
    print(f"修改后 - peking.country = {peking.country}")

def special_methods():
    """特殊方法 (魔术方法) / Special Methods (Magic Methods)"""
    print("\n=== 特殊方法 Special Methods (Magic Methods) ===")
    
    class Book:
        """书籍类 - 演示特殊方法"""
        
        def __init__(self, title, author, pages, price):
            self.title = title
            self.author = author
            self.pages = pages
            self.price = price
        
        def __str__(self):
            """字符串表示 - 用户友好 / String representation - user-friendly"""
            return f"《{self.title}》 by {self.author}"
        
        def __repr__(self):
            """字符串表示 - 开发者友好 / String representation - developer-friendly"""
            return f"Book('{self.title}', '{self.author}', {self.pages}, {self.price})"
        
        def __len__(self):
            """返回页数 / Return page count"""
            return self.pages
        
        def __eq__(self, other):
            """相等比较 / Equality comparison"""
            if isinstance(other, Book):
                return (self.title == other.title and 
                       self.author == other.author)
            return False
        
        def __lt__(self, other):
            """小于比较 - 按价格 / Less than comparison - by price"""
            if isinstance(other, Book):
                return self.price < other.price
            return NotImplemented
        
        def __add__(self, other):
            """加法操作 - 合并页数 / Addition operation - combine pages"""
            if isinstance(other, Book):
                combined_title = f"{self.title} & {other.title}"
                combined_author = f"{self.author} & {other.author}"
                combined_pages = self.pages + other.pages
                combined_price = self.price + other.price
                return Book(combined_title, combined_author, combined_pages, combined_price)
            return NotImplemented
        
        def __getitem__(self, key):
            """索引访问 / Index access"""
            if key == 0:
                return self.title
            elif key == 1:
                return self.author
            elif key == 2:
                return self.pages
            elif key == 3:
                return self.price
            else:
                raise IndexError("Book index out of range")
        
        def __contains__(self, item):
            """成员测试 / Membership test"""
            return item.lower() in self.title.lower()
    
    print("1. 创建书籍对象:")
    book1 = Book("Python编程", "张三", 300, 59.9)
    book2 = Book("数据结构", "李四", 250, 45.0)
    book3 = Book("Python编程", "张三", 300, 59.9)  # 与book1相同
    
    print(f"book1: {book1}")  # 调用 __str__
    print(f"repr(book1): {repr(book1)}")  # 调用 __repr__
    
    print("\n2. 特殊方法演示:")
    print(f"len(book1): {len(book1)} 页")  # 调用 __len__
    print(f"book1 == book3: {book1 == book3}")  # 调用 __eq__
    print(f"book1 == book2: {book1 == book2}")
    print(f"book1 < book2: {book1 < book2}")  # 调用 __lt__
    
    print("\n3. 索引访问:")
    print(f"book1[0]: {book1[0]}")  # 调用 __getitem__
    print(f"book1[1]: {book1[1]}")
    
    print("\n4. 成员测试:")
    print(f"'Python' in book1: {'Python' in book1}")  # 调用 __contains__
    print(f"'Java' in book1: {'Java' in book1}")
    
    print("\n5. 加法操作:")
    combined_book = book1 + book2  # 调用 __add__
    print(f"合并后的书: {combined_book}")
    print(f"合并后页数: {len(combined_book)}")

def property_decorators():
    """属性装饰器 / Property Decorators"""
    print("\n=== 属性装饰器 Property Decorators ===")
    
    class Circle:
        """圆形类 - 演示属性装饰器"""
        
        def __init__(self, radius):
            self._radius = radius  # 私有属性约定 (以下划线开头)
        
        @property
        def radius(self):
            """半径属性的getter / Radius property getter"""
            return self._radius
        
        @radius.setter
        def radius(self, value):
            """半径属性的setter / Radius property setter"""
            if value <= 0:
                raise ValueError("半径必须大于0")
            self._radius = value
        
        @property
        def diameter(self):
            """直径属性 - 只读 / Diameter property - read-only"""
            return self._radius * 2
        
        @property
        def area(self):
            """面积属性 - 只读 / Area property - read-only"""
            return math.pi * self._radius ** 2
        
        @property
        def circumference(self):
            """周长属性 - 只读 / Circumference property - read-only"""
            return 2 * math.pi * self._radius
        
        def __str__(self):
            return f"Circle(radius={self._radius})"
    
    print("1. 使用属性装饰器:")
    circle = Circle(5)
    
    print(f"圆形: {circle}")
    print(f"半径: {circle.radius}")
    print(f"直径: {circle.diameter}")
    print(f"面积: {circle.area:.2f}")
    print(f"周长: {circle.circumference:.2f}")
    
    print("\n2. 修改属性:")
    circle.radius = 10
    print(f"修改半径后: {circle}")
    print(f"新面积: {circle.area:.2f}")
    
    print("\n3. 属性验证:")
    try:
        circle.radius = -5
    except ValueError as e:
        print(f"设置无效半径时的错误: {e}")
    
    # 尝试修改只读属性 / Try to modify read-only property
    try:
        circle.area = 100  # 这会引发AttributeError
    except AttributeError as e:
        print(f"尝试修改只读属性时的错误: can't set attribute")

def encapsulation_example():
    """封装示例 / Encapsulation Example"""
    print("\n=== 封装示例 Encapsulation Example ===")
    
    class BankAccount:
        """银行账户类 - 演示封装"""
        
        def __init__(self, account_number, initial_balance=0):
            self._account_number = account_number  # 受保护属性
            self.__balance = initial_balance       # 私有属性
            self.__transaction_history = []        # 私有属性
        
        @property
        def account_number(self):
            """账户号码 - 只读"""
            return self._account_number
        
        @property
        def balance(self):
            """余额 - 只读"""
            return self.__balance
        
        def deposit(self, amount):
            """存款 / Deposit"""
            if amount <= 0:
                raise ValueError("存款金额必须大于0")
            
            self.__balance += amount
            self.__transaction_history.append({
                "type": "deposit",
                "amount": amount,
                "balance": self.__balance,
                "timestamp": datetime.now()
            })
            print(f"存款 ¥{amount:.2f} 成功，当前余额: ¥{self.__balance:.2f}")
        
        def withdraw(self, amount):
            """取款 / Withdraw"""
            if amount <= 0:
                raise ValueError("取款金额必须大于0")
            
            if amount > self.__balance:
                raise ValueError("余额不足")
            
            self.__balance -= amount
            self.__transaction_history.append({
                "type": "withdraw",
                "amount": amount,
                "balance": self.__balance,
                "timestamp": datetime.now()
            })
            print(f"取款 ¥{amount:.2f} 成功，当前余额: ¥{self.__balance:.2f}")
        
        def get_transaction_history(self, limit=5):
            """获取交易历史 / Get transaction history"""
            return self.__transaction_history[-limit:]
        
        def __str__(self):
            return f"BankAccount({self._account_number}, balance=¥{self.__balance:.2f})"
    
    print("1. 创建银行账户:")
    account = BankAccount("*********", 1000)
    print(f"账户信息: {account}")
    print(f"账户号码: {account.account_number}")
    print(f"当前余额: ¥{account.balance:.2f}")
    
    print("\n2. 进行交易:")
    account.deposit(500)
    account.withdraw(200)
    account.deposit(100)
    
    print("\n3. 查看交易历史:")
    history = account.get_transaction_history()
    for i, transaction in enumerate(history, 1):
        print(f"  {i}. {transaction['type']}: ¥{transaction['amount']:.2f}, "
              f"余额: ¥{transaction['balance']:.2f}")
    
    print("\n4. 封装保护:")
    print("- 无法直接修改余额 (私有属性)")
    print("- 无法直接访问交易历史 (私有属性)")
    print("- 只能通过公共方法进行操作")
    
    # 演示私有属性的访问限制 / Demonstrate private attribute access restrictions
    try:
        print(f"尝试直接访问私有属性: {account.__balance}")
    except AttributeError:
        print("无法直接访问私有属性 __balance")
    
    # 但是可以通过名称修饰访问 (不推荐) / But can access through name mangling (not recommended)
    print(f"通过名称修饰访问 (不推荐): {account._BankAccount__balance}")

def inheritance_example():
    """继承示例 / Inheritance Example"""
    print("\n=== 继承示例 Inheritance Example ===")

    # 基类 / Base class
    class Animal:
        """动物基类"""

        def __init__(self, name, species):
            self.name = name
            self.species = species
            self.is_alive = True

        def eat(self):
            """吃东西 / Eat"""
            return f"{self.name} 正在吃东西"

        def sleep(self):
            """睡觉 / Sleep"""
            return f"{self.name} 正在睡觉"

        def make_sound(self):
            """发出声音 - 基类方法 / Make sound - base class method"""
            return f"{self.name} 发出了声音"

        def __str__(self):
            return f"{self.species}: {self.name}"

    # 派生类 / Derived classes
    class Dog(Animal):
        """狗类 - 继承自Animal"""

        def __init__(self, name, breed):
            super().__init__(name, "狗")  # 调用父类构造函数
            self.breed = breed

        def make_sound(self):
            """重写父类方法 / Override parent method"""
            return f"{self.name} 汪汪叫"

        def fetch(self):
            """狗特有的方法 / Dog-specific method"""
            return f"{self.name} 去捡球了"

    class Cat(Animal):
        """猫类 - 继承自Animal"""

        def __init__(self, name, color):
            super().__init__(name, "猫")
            self.color = color

        def make_sound(self):
            """重写父类方法 / Override parent method"""
            return f"{self.name} 喵喵叫"

        def climb(self):
            """猫特有的方法 / Cat-specific method"""
            return f"{self.name} 爬到了树上"

    class Bird(Animal):
        """鸟类 - 继承自Animal"""

        def __init__(self, name, can_fly=True):
            super().__init__(name, "鸟")
            self.can_fly = can_fly

        def make_sound(self):
            return f"{self.name} 叽叽喳喳"

        def fly(self):
            """鸟特有的方法 / Bird-specific method"""
            if self.can_fly:
                return f"{self.name} 飞起来了"
            else:
                return f"{self.name} 不会飞"

    print("1. 创建不同的动物:")
    dog = Dog("旺财", "金毛")
    cat = Cat("咪咪", "橘色")
    bird = Bird("小黄", True)

    animals = [dog, cat, bird]

    for animal in animals:
        print(f"  {animal}")
        print(f"    {animal.eat()}")
        print(f"    {animal.make_sound()}")  # 多态 - 不同类有不同实现

    print("\n2. 调用特有方法:")
    print(f"  {dog.fetch()}")
    print(f"  {cat.climb()}")
    print(f"  {bird.fly()}")

    print("\n3. 检查继承关系:")
    print(f"  isinstance(dog, Dog): {isinstance(dog, Dog)}")
    print(f"  isinstance(dog, Animal): {isinstance(dog, Animal)}")
    print(f"  issubclass(Dog, Animal): {issubclass(Dog, Animal)}")

def multiple_inheritance():
    """多重继承 / Multiple Inheritance"""
    print("\n=== 多重继承 Multiple Inheritance ===")

    class Flyable:
        """可飞行的混入类 / Flyable mixin class"""

        def fly(self):
            return f"{self.name} 正在飞行"

        def land(self):
            return f"{self.name} 降落了"

    class Swimmable:
        """可游泳的混入类 / Swimmable mixin class"""

        def swim(self):
            return f"{self.name} 正在游泳"

        def dive(self):
            return f"{self.name} 潜水了"

    class Duck(Animal, Flyable, Swimmable):
        """鸭子类 - 多重继承"""

        def __init__(self, name):
            Animal.__init__(self, name, "鸭子")

        def make_sound(self):
            return f"{self.name} 嘎嘎叫"

    class Penguin(Animal, Swimmable):
        """企鹅类 - 会游泳但不会飞"""

        def __init__(self, name):
            Animal.__init__(self, name, "企鹅")

        def make_sound(self):
            return f"{self.name} 发出企鹅叫声"

    print("1. 多重继承示例:")
    duck = Duck("唐老鸭")
    penguin = Penguin("波波")

    print(f"  鸭子: {duck}")
    print(f"    {duck.make_sound()}")
    print(f"    {duck.fly()}")
    print(f"    {duck.swim()}")

    print(f"  企鹅: {penguin}")
    print(f"    {penguin.make_sound()}")
    print(f"    {penguin.swim()}")
    print(f"    {penguin.dive()}")

    print("\n2. 方法解析顺序 (MRO):")
    print(f"  Duck MRO: {Duck.__mro__}")
    print(f"  Penguin MRO: {Penguin.__mro__}")

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python面向对象编程学习示例")
    print("Python Object-Oriented Programming Learning Examples")
    print("=" * 60)

    basic_class_concepts()
    class_attributes_vs_instance_attributes()
    special_methods()
    property_decorators()
    encapsulation_example()
    inheritance_example()
    multiple_inheritance()

    print("\n" + "=" * 60)
    print("面向对象编程基础学习完成！OOP basics learning completed!")
