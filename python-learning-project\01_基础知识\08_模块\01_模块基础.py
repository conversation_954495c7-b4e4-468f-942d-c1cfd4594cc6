#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块 (Modules) - Python模块系统
Modules - Python Module System

模块是Python代码组织的基本单位，用于将相关的函数、类和变量组织在一起，实现代码重用和命名空间管理。
Modules are the basic units of Python code organization, used to organize related functions, classes, and variables together, achieving code reuse and namespace management.
"""

import os
import sys
import math
import random
from datetime import datetime, timedelta
from collections import Counter
import json

def basic_import_concepts():
    """基本导入概念 / Basic Import Concepts"""
    print("=== 基本导入概念 Basic Import Concepts ===")
    
    print("1. 什么是模块:")
    print("   - 模块是包含Python代码的文件 (.py文件)")
    print("   - 模块可以包含函数、类、变量和可执行语句")
    print("   - 模块提供了代码重用和命名空间隔离")
    
    print("\n2. 导入方式:")
    print("   import module_name          # 导入整个模块")
    print("   from module import name     # 导入特定名称")
    print("   from module import *        # 导入所有公共名称 (不推荐)")
    print("   import module as alias      # 使用别名导入")
    print("   from module import name as alias  # 导入并重命名")
    
    print("\n3. 标准库模块示例:")
    
    # math模块 / math module
    print(f"   math.pi = {math.pi}")
    print(f"   math.sqrt(16) = {math.sqrt(16)}")
    
    # random模块 / random module
    print(f"   random.randint(1, 10) = {random.randint(1, 10)}")
    
    # datetime模块 / datetime module
    now = datetime.now()
    print(f"   当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # os模块 / os module
    print(f"   当前工作目录: {os.getcwd()}")

def create_custom_module():
    """创建自定义模块 / Create Custom Module"""
    print("\n=== 创建自定义模块 Create Custom Module ===")
    
    # 创建一个简单的工具模块 / Create a simple utility module
    utils_module_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
utils.py - 实用工具模块
Utility module with common functions
"""

# 模块级变量 / Module-level variables
VERSION = "1.0.0"
AUTHOR = "Python学习者"

def greet(name, language="zh"):
    """多语言问候函数 / Multi-language greeting function"""
    greetings = {
        "zh": f"你好, {name}!",
        "en": f"Hello, {name}!",
        "es": f"¡Hola, {name}!",
        "fr": f"Bonjour, {name}!"
    }
    return greetings.get(language, greetings["en"])

def calculate_area(shape, **kwargs):
    """计算面积 / Calculate area"""
    if shape == "rectangle":
        return kwargs.get("width", 0) * kwargs.get("height", 0)
    elif shape == "circle":
        import math
        radius = kwargs.get("radius", 0)
        return math.pi * radius ** 2
    elif shape == "triangle":
        base = kwargs.get("base", 0)
        height = kwargs.get("height", 0)
        return 0.5 * base * height
    else:
        return 0

class SimpleCalculator:
    """简单计算器类 / Simple calculator class"""
    
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def subtract(self, a, b):
        result = a - b
        self.history.append(f"{a} - {b} = {result}")
        return result
    
    def get_history(self):
        return self.history.copy()

# 模块初始化代码 / Module initialization code
print(f"utils模块已加载，版本: {VERSION}")

if __name__ == "__main__":
    # 模块作为脚本运行时的代码 / Code when module runs as script
    print("utils模块测试:")
    print(greet("张三"))
    print(f"圆形面积: {calculate_area('circle', radius=5)}")
    
    calc = SimpleCalculator()
    calc.add(10, 5)
    calc.subtract(20, 8)
    print("计算历史:", calc.get_history())
'''
    
    # 写入模块文件 / Write module file
    with open('utils.py', 'w', encoding='utf-8') as f:
        f.write(utils_module_content)
    
    print("1. 创建了自定义模块 utils.py")
    print("   包含函数: greet(), calculate_area()")
    print("   包含类: SimpleCalculator")
    print("   包含变量: VERSION, AUTHOR")

def import_custom_module():
    """导入自定义模块 / Import Custom Module"""
    print("\n=== 导入自定义模块 Import Custom Module ===")
    
    # 确保模块在Python路径中 / Ensure module is in Python path
    if '.' not in sys.path:
        sys.path.insert(0, '.')
    
    print("1. 不同的导入方式:")
    
    # 方式1: 导入整个模块 / Method 1: Import entire module
    import utils
    print(f"   import utils: {utils.greet('Alice', 'en')}")
    print(f"   模块版本: {utils.VERSION}")
    
    # 方式2: 导入特定函数 / Method 2: Import specific function
    from utils import calculate_area
    area = calculate_area('rectangle', width=10, height=5)
    print(f"   from utils import calculate_area: 矩形面积 = {area}")
    
    # 方式3: 导入并重命名 / Method 3: Import with alias
    from utils import SimpleCalculator as Calc
    calculator = Calc()
    result = calculator.add(15, 25)
    print(f"   使用别名导入的计算器: 15 + 25 = {result}")
    
    print("\n2. 模块属性:")
    print(f"   模块名称: {utils.__name__}")
    print(f"   模块文件: {utils.__file__}")
    print(f"   模块文档: {utils.__doc__[:50]}...")
    
    print("\n3. 查看模块内容:")
    module_contents = [name for name in dir(utils) if not name.startswith('_')]
    print(f"   公共属性和方法: {module_contents}")

def package_example():
    """包示例 / Package Example"""
    print("\n=== 包示例 Package Example ===")
    
    # 创建包结构 / Create package structure
    package_dir = "mypackage"
    if not os.path.exists(package_dir):
        os.makedirs(package_dir)
    
    # 创建 __init__.py / Create __init__.py
    init_content = '''"""
mypackage - 示例包
Example package for learning
"""

# 包级别的变量 / Package-level variables
__version__ = "1.0.0"
__author__ = "Python学习者"

# 从子模块导入 / Import from submodules
from .math_utils import add, multiply
from .string_utils import reverse_string, count_words

# 定义 __all__ 来控制 from package import * 的行为
__all__ = ['add', 'multiply', 'reverse_string', 'count_words']

print(f"mypackage包已加载，版本: {__version__}")
'''
    
    with open(f'{package_dir}/__init__.py', 'w', encoding='utf-8') as f:
        f.write(init_content)
    
    # 创建数学工具子模块 / Create math utilities submodule
    math_utils_content = '''"""
math_utils.py - 数学工具函数
Mathematical utility functions
"""

def add(a, b):
    """加法函数 / Addition function"""
    return a + b

def multiply(a, b):
    """乘法函数 / Multiplication function"""
    return a * b

def factorial(n):
    """阶乘函数 / Factorial function"""
    if n <= 1:
        return 1
    return n * factorial(n - 1)

def is_prime(n):
    """判断是否为质数 / Check if number is prime"""
    if n < 2:
        return False
    for i in range(2, int(n ** 0.5) + 1):
        if n % i == 0:
            return False
    return True
'''
    
    with open(f'{package_dir}/math_utils.py', 'w', encoding='utf-8') as f:
        f.write(math_utils_content)
    
    # 创建字符串工具子模块 / Create string utilities submodule
    string_utils_content = '''"""
string_utils.py - 字符串工具函数
String utility functions
"""

def reverse_string(s):
    """反转字符串 / Reverse string"""
    return s[::-1]

def count_words(text):
    """统计单词数量 / Count words"""
    return len(text.split())

def capitalize_words(text):
    """首字母大写 / Capitalize words"""
    return ' '.join(word.capitalize() for word in text.split())

def remove_duplicates(text):
    """移除重复字符 / Remove duplicate characters"""
    return ''.join(dict.fromkeys(text))
'''
    
    with open(f'{package_dir}/string_utils.py', 'w', encoding='utf-8') as f:
        f.write(string_utils_content)
    
    print("1. 创建了包结构:")
    print("   mypackage/")
    print("   ├── __init__.py")
    print("   ├── math_utils.py")
    print("   └── string_utils.py")
    
    print("\n2. 导入包:")
    try:
        # 导入包 / Import package
        import mypackage
        print(f"   包版本: {mypackage.__version__}")
        
        # 使用包中的函数 / Use functions from package
        result1 = mypackage.add(10, 20)
        result2 = mypackage.multiply(5, 6)
        result3 = mypackage.reverse_string("Hello")
        result4 = mypackage.count_words("Hello world Python")
        
        print(f"   add(10, 20) = {result1}")
        print(f"   multiply(5, 6) = {result2}")
        print(f"   reverse_string('Hello') = {result3}")
        print(f"   count_words('Hello world Python') = {result4}")
        
        # 导入特定子模块 / Import specific submodule
        from mypackage import math_utils
        print(f"   factorial(5) = {math_utils.factorial(5)}")
        print(f"   is_prime(17) = {math_utils.is_prime(17)}")
        
    except ImportError as e:
        print(f"   导入错误: {e}")

def module_search_path():
    """模块搜索路径 / Module Search Path"""
    print("\n=== 模块搜索路径 Module Search Path ===")
    
    print("1. Python模块搜索顺序:")
    print("   1) 当前目录")
    print("   2) PYTHONPATH环境变量指定的目录")
    print("   3) 标准库目录")
    print("   4) site-packages目录 (第三方包)")
    
    print("\n2. 当前sys.path内容:")
    for i, path in enumerate(sys.path[:5]):  # 只显示前5个
        print(f"   {i+1}. {path}")
    if len(sys.path) > 5:
        print(f"   ... 还有 {len(sys.path) - 5} 个路径")
    
    print("\n3. 动态修改搜索路径:")
    original_path = sys.path.copy()
    
    # 添加新路径 / Add new path
    new_path = "/custom/module/path"
    sys.path.insert(0, new_path)
    print(f"   添加路径: {new_path}")
    print(f"   新的第一个路径: {sys.path[0]}")
    
    # 恢复原始路径 / Restore original path
    sys.path = original_path
    print("   已恢复原始搜索路径")

def module_reloading():
    """模块重新加载 / Module Reloading"""
    print("\n=== 模块重新加载 Module Reloading ===")
    
    print("1. 模块加载机制:")
    print("   - 模块只在第一次导入时执行")
    print("   - 后续导入会使用缓存的模块对象")
    print("   - sys.modules 存储已加载的模块")
    
    print("\n2. 查看已加载的模块 (部分):")
    loaded_modules = list(sys.modules.keys())[:10]
    for module in loaded_modules:
        print(f"   {module}")
    print(f"   ... 总共 {len(sys.modules)} 个已加载模块")
    
    print("\n3. 重新加载模块:")
    print("   使用 importlib.reload() 可以重新加载模块")
    print("   注意: 重新加载可能导致意外行为，谨慎使用")
    
    # 演示重新加载 / Demonstrate reloading
    import importlib
    try:
        import utils
        print(f"   utils模块版本: {utils.VERSION}")
        
        # 重新加载 / Reload
        importlib.reload(utils)
        print("   utils模块已重新加载")
        
    except Exception as e:
        print(f"   重新加载失败: {e}")

def cleanup_files():
    """清理创建的文件 / Cleanup Created Files"""
    print("\n=== 清理文件 Cleanup Files ===")
    
    files_to_remove = ['utils.py']
    dirs_to_remove = ['mypackage']
    
    # 删除文件 / Remove files
    for file in files_to_remove:
        if os.path.exists(file):
            os.remove(file)
            print(f"删除文件: {file}")
    
    # 删除目录 / Remove directories
    import shutil
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"删除目录: {dir_name}")
    
    # 清理模块缓存 / Clear module cache
    modules_to_remove = ['utils', 'mypackage', 'mypackage.math_utils', 'mypackage.string_utils']
    for module in modules_to_remove:
        if module in sys.modules:
            del sys.modules[module]
            print(f"清理模块缓存: {module}")

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python模块系统学习示例")
    print("Python Module System Learning Examples")
    print("=" * 50)
    
    basic_import_concepts()
    create_custom_module()
    import_custom_module()
    package_example()
    module_search_path()
    module_reloading()
    
    print("\n" + "=" * 50)
    print("模块系统学习完成！Module system learning completed!")
    
    # 清理创建的文件 / Cleanup created files
    cleanup_files()
