#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
for循环 (For Loop) - Python迭代循环
For Loop - Python Iteration Loop

for循环用于遍历序列（如列表、元组、字符串）或其他可迭代对象。
For loops are used to iterate over sequences (like lists, tuples, strings) or other iterable objects.
"""

def basic_for_loops():
    """基本for循环 / Basic For Loops"""
    print("=== 基本for循环 Basic For Loops ===")
    
    # 遍历列表 / Iterate over list
    fruits = ["苹果", "香蕉", "橙子", "葡萄"]
    print("水果列表:")
    for fruit in fruits:
        print(f"- {fruit}")
    
    # 遍历字符串 / Iterate over string
    word = "Python"
    print(f"\n'{word}'的每个字符:")
    for char in word:
        print(f"'{char}'", end=" ")
    print()
    
    # 遍历元组 / Iterate over tuple
    coordinates = (10, 20, 30)
    print(f"\n坐标 {coordinates}:")
    for coord in coordinates:
        print(f"坐标值: {coord}")
    
    # 遍历字典 / Iterate over dictionary
    student = {"姓名": "张三", "年龄": 20, "专业": "计算机"}
    print("\n学生信息:")
    for key in student:
        print(f"{key}: {student[key]}")

def range_function():
    """range函数 / Range Function"""
    print("\n=== range函数 Range Function ===")
    
    # 基本range用法 / Basic range usage
    print("0到4的数字:")
    for i in range(5):
        print(i, end=" ")
    print()
    
    # 指定起始和结束 / Specify start and end
    print("\n2到7的数字:")
    for i in range(2, 8):
        print(i, end=" ")
    print()
    
    # 指定步长 / Specify step
    print("\n0到10的偶数:")
    for i in range(0, 11, 2):
        print(i, end=" ")
    print()
    
    # 倒序range / Reverse range
    print("\n10到1的倒序:")
    for i in range(10, 0, -1):
        print(i, end=" ")
    print()
    
    # 生成列表 / Generate list
    squares = [i**2 for i in range(1, 6)]
    print(f"\n1到5的平方: {squares}")

def enumerate_function():
    """enumerate函数 / Enumerate Function"""
    print("\n=== enumerate函数 Enumerate Function ===")
    
    # 基本enumerate用法 / Basic enumerate usage
    colors = ["红色", "绿色", "蓝色", "黄色"]
    print("颜色列表(带索引):")
    for index, color in enumerate(colors):
        print(f"{index}: {color}")
    
    # 指定起始索引 / Specify starting index
    print("\n从1开始的索引:")
    for index, color in enumerate(colors, start=1):
        print(f"第{index}个颜色: {color}")
    
    # 实际应用：处理文件行 / Practical use: processing file lines
    lines = ["第一行", "第二行", "第三行", "第四行"]
    print("\n文件内容(带行号):")
    for line_num, content in enumerate(lines, start=1):
        print(f"行{line_num}: {content}")

def zip_function():
    """zip函数 / Zip Function"""
    print("\n=== zip函数 Zip Function ===")
    
    # 基本zip用法 / Basic zip usage
    names = ["张三", "李四", "王五"]
    ages = [20, 25, 30]
    cities = ["北京", "上海", "广州"]
    
    print("学生信息:")
    for name, age, city in zip(names, ages, cities):
        print(f"姓名: {name}, 年龄: {age}, 城市: {city}")
    
    # 不等长序列 / Unequal length sequences
    numbers1 = [1, 2, 3, 4, 5]
    numbers2 = [10, 20, 30]
    
    print("\n不等长序列配对:")
    for a, b in zip(numbers1, numbers2):
        print(f"{a} + {b} = {a + b}")
    
    # 创建字典 / Create dictionary
    keys = ["name", "age", "city"]
    values = ["Alice", 28, "深圳"]
    person = dict(zip(keys, values))
    print(f"\n创建的字典: {person}")
    
    # 转置矩阵 / Transpose matrix
    matrix = [[1, 2, 3], [4, 5, 6], [7, 8, 9]]
    transposed = list(zip(*matrix))
    print(f"\n原矩阵: {matrix}")
    print(f"转置后: {list(transposed)}")

def nested_loops():
    """嵌套循环 / Nested Loops"""
    print("\n=== 嵌套循环 Nested Loops ===")
    
    # 乘法表 / Multiplication table
    print("九九乘法表:")
    for i in range(1, 10):
        for j in range(1, i + 1):
            print(f"{j}×{i}={i*j:2d}", end="  ")
        print()
    
    # 矩阵遍历 / Matrix traversal
    matrix = [
        [1, 2, 3],
        [4, 5, 6],
        [7, 8, 9]
    ]
    
    print("\n矩阵遍历:")
    for i, row in enumerate(matrix):
        for j, value in enumerate(row):
            print(f"matrix[{i}][{j}] = {value}")
    
    # 组合生成 / Combination generation
    print("\n颜色和尺寸组合:")
    colors = ["红", "蓝", "绿"]
    sizes = ["S", "M", "L"]
    
    for color in colors:
        for size in sizes:
            print(f"{color}色-{size}码", end="  ")
        print()

def loop_control():
    """循环控制 / Loop Control"""
    print("\n=== 循环控制 Loop Control ===")
    
    # break语句 / break statement
    print("break示例 - 找到第一个偶数:")
    numbers = [1, 3, 5, 8, 9, 12, 15]
    for num in numbers:
        if num % 2 == 0:
            print(f"找到第一个偶数: {num}")
            break
        print(f"检查数字: {num}")
    
    # continue语句 / continue statement
    print("\ncontinue示例 - 只打印偶数:")
    for num in range(1, 11):
        if num % 2 != 0:
            continue  # 跳过奇数
        print(f"偶数: {num}")
    
    # else子句 / else clause
    print("\nelse子句示例 - 查找质数:")
    def is_prime(n):
        if n < 2:
            return False
        for i in range(2, int(n**0.5) + 1):
            if n % i == 0:
                return False
        else:
            return True  # 循环正常结束，没有找到因子
    
    test_numbers = [17, 18, 19, 20]
    for num in test_numbers:
        if is_prime(num):
            print(f"{num} 是质数")
        else:
            print(f"{num} 不是质数")

def practical_examples():
    """实际应用示例 / Practical Examples"""
    print("\n=== 实际应用示例 Practical Examples ===")
    
    # 1. 数据统计 / Data statistics
    print("1. 学生成绩统计:")
    students_scores = [
        ("张三", [85, 92, 78, 90]),
        ("李四", [88, 85, 92, 87]),
        ("王五", [92, 88, 85, 91])
    ]
    
    for name, scores in students_scores:
        total = sum(scores)
        average = total / len(scores)
        max_score = max(scores)
        min_score = min(scores)
        
        print(f"{name}: 总分{total}, 平均{average:.1f}, 最高{max_score}, 最低{min_score}")
    
    # 2. 文本处理 / Text processing
    print("\n2. 文本分析:")
    text = "Python是一种强大的编程语言Python很容易学习Python应用广泛"
    
    # 字符统计 / Character statistics
    char_count = {}
    for char in text:
        char_count[char] = char_count.get(char, 0) + 1
    
    print("字符出现次数:")
    for char, count in sorted(char_count.items()):
        if char != ' ':  # 忽略空格
            print(f"'{char}': {count}次", end="  ")
    print()
    
    # 单词统计 / Word statistics
    words = text.split()
    word_count = {}
    for word in words:
        word_count[word] = word_count.get(word, 0) + 1
    
    print("\n单词出现次数:")
    for word, count in word_count.items():
        print(f"'{word}': {count}次")
    
    # 3. 数据验证 / Data validation
    print("\n3. 数据验证:")
    user_data = [
        {"name": "张三", "age": 25, "email": "<EMAIL>"},
        {"name": "", "age": 17, "email": "invalid-email"},
        {"name": "李四", "age": 30, "email": "<EMAIL>"},
        {"name": "王五", "age": -5, "email": "<EMAIL>"}
    ]
    
    valid_users = []
    for i, user in enumerate(user_data):
        errors = []
        
        # 验证姓名 / Validate name
        if not user["name"]:
            errors.append("姓名不能为空")
        
        # 验证年龄 / Validate age
        if user["age"] < 18:
            errors.append("年龄必须大于等于18")
        
        # 验证邮箱 / Validate email
        if "@" not in user["email"] or "." not in user["email"]:
            errors.append("邮箱格式不正确")
        
        if errors:
            print(f"用户{i+1} 验证失败: {', '.join(errors)}")
        else:
            valid_users.append(user)
            print(f"用户{i+1} 验证通过: {user['name']}")
    
    print(f"\n有效用户数量: {len(valid_users)}")

def list_comprehensions():
    """列表推导式 / List Comprehensions"""
    print("\n=== 列表推导式 List Comprehensions ===")
    
    # 基本列表推导式 / Basic list comprehension
    squares = [x**2 for x in range(1, 6)]
    print(f"1-5的平方: {squares}")
    
    # 带条件的列表推导式 / List comprehension with condition
    even_squares = [x**2 for x in range(1, 11) if x % 2 == 0]
    print(f"1-10中偶数的平方: {even_squares}")
    
    # 字符串处理 / String processing
    words = ["hello", "world", "python", "programming"]
    upper_words = [word.upper() for word in words if len(word) > 5]
    print(f"长度大于5的单词(大写): {upper_words}")
    
    # 嵌套列表推导式 / Nested list comprehension
    matrix = [[i*j for j in range(1, 4)] for i in range(1, 4)]
    print(f"3x3乘法矩阵: {matrix}")
    
    # 展平嵌套列表 / Flatten nested list
    nested_list = [[1, 2, 3], [4, 5, 6], [7, 8, 9]]
    flattened = [item for sublist in nested_list for item in sublist]
    print(f"展平后的列表: {flattened}")
    
    # 字典推导式 / Dictionary comprehension
    word_lengths = {word: len(word) for word in words}
    print(f"单词长度字典: {word_lengths}")
    
    # 集合推导式 / Set comprehension
    unique_lengths = {len(word) for word in words}
    print(f"唯一的单词长度: {unique_lengths}")

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python for循环学习示例")
    print("Python For Loop Learning Examples")
    print("=" * 50)
    
    basic_for_loops()
    range_function()
    enumerate_function()
    zip_function()
    nested_loops()
    loop_control()
    practical_examples()
    list_comprehensions()
    
    print("\n" + "=" * 50)
    print("for循环学习完成！For loop learning completed!")
