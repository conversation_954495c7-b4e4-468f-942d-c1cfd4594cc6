#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字典 (Dictionary) - Python键值对数据结构
Dictionaries - Python Key-Value Data Structure

字典是Python中用于存储键值对的可变数据结构，类似于其他语言中的哈希表或映射。
Dictionaries are mutable data structures in Python for storing key-value pairs, similar to hash tables or maps in other languages.
"""

def basic_dict_operations():
    """基本字典操作 / Basic Dictionary Operations"""
    print("=== 基本字典操作 Basic Dictionary Operations ===")
    
    # 创建字典 / Creating dictionaries
    empty_dict = {}  # 空字典 / Empty dictionary
    student = {"姓名": "张三", "年龄": 20, "专业": "计算机科学"}  # 学生信息 / Student info
    scores = dict(数学=85, 英语=92, 物理=78)  # 使用dict()函数 / Using dict() function
    
    print(f"空字典: {empty_dict}")
    print(f"学生信息: {student}")
    print(f"成绩: {scores}")
    
    # 访问值 / Accessing values
    print(f"学生姓名: {student['姓名']}")
    print(f"数学成绩: {scores['数学']}")
    
    # 安全访问(使用get方法) / Safe access (using get method)
    print(f"化学成绩: {scores.get('化学', '未考试')}")  # 提供默认值 / Provide default value
    
    # 字典长度 / Dictionary length
    print(f"学生信息项数: {len(student)}")
    
    return student, scores

def dict_modification():
    """字典修改操作 / Dictionary Modification Operations"""
    print("\n=== 字典修改操作 Dictionary Modification Operations ===")
    
    student = {"姓名": "李四", "年龄": 21}
    print(f"原始学生信息: {student}")
    
    # 添加新键值对 / Adding new key-value pairs
    student["专业"] = "软件工程"
    student["学号"] = "2023001"
    print(f"添加专业和学号: {student}")
    
    # 修改现有值 / Modifying existing values
    student["年龄"] = 22
    print(f"修改年龄: {student}")
    
    # 删除键值对 / Removing key-value pairs
    del student["学号"]  # 使用del删除 / Delete using del
    print(f"删除学号: {student}")
    
    removed_age = student.pop("年龄")  # 删除并返回值 / Remove and return value
    print(f"删除的年龄: {removed_age}, 剩余信息: {student}")
    
    # 批量更新 / Batch update
    new_info = {"年龄": 23, "城市": "北京", "爱好": "编程"}
    student.update(new_info)
    print(f"批量更新后: {student}")
    
    return student

def dict_methods():
    """字典常用方法 / Common Dictionary Methods"""
    print("\n=== 字典常用方法 Common Dictionary Methods ===")
    
    inventory = {"苹果": 50, "香蕉": 30, "橙子": 25, "葡萄": 40}
    print(f"库存: {inventory}")
    
    # 获取所有键 / Get all keys
    keys = list(inventory.keys())
    print(f"所有商品: {keys}")
    
    # 获取所有值 / Get all values
    values = list(inventory.values())
    print(f"所有数量: {values}")
    
    # 获取所有键值对 / Get all key-value pairs
    items = list(inventory.items())
    print(f"所有键值对: {items}")
    
    # 检查键是否存在 / Check if key exists
    print(f"苹果在库存中: {'苹果' in inventory}")
    print(f"西瓜在库存中: {'西瓜' in inventory}")
    
    # 清空字典 / Clear dictionary
    temp_dict = inventory.copy()  # 先复制 / Copy first
    temp_dict.clear()
    print(f"清空后的临时字典: {temp_dict}")
    print(f"原字典未变: {inventory}")

def dict_comprehension():
    """字典推导式 / Dictionary Comprehension"""
    print("\n=== 字典推导式 Dictionary Comprehension ===")
    
    # 基本字典推导式 / Basic dictionary comprehension
    squares = {x: x**2 for x in range(1, 6)}
    print(f"数字的平方: {squares}")
    
    # 带条件的字典推导式 / Dictionary comprehension with condition
    even_squares = {x: x**2 for x in range(1, 11) if x % 2 == 0}
    print(f"偶数的平方: {even_squares}")
    
    # 字符串处理 / String processing
    words = ["hello", "world", "python"]
    word_lengths = {word: len(word) for word in words}
    print(f"单词长度: {word_lengths}")
    
    # 转换现有字典 / Transform existing dictionary
    celsius = {"北京": 25, "上海": 28, "广州": 32}
    fahrenheit = {city: (temp * 9/5) + 32 for city, temp in celsius.items()}
    print(f"摄氏度: {celsius}")
    print(f"华氏度: {fahrenheit}")

def nested_dictionaries():
    """嵌套字典 / Nested Dictionaries"""
    print("\n=== 嵌套字典 Nested Dictionaries ===")
    
    # 学生成绩管理系统 / Student grade management system
    students = {
        "张三": {
            "年龄": 20,
            "专业": "计算机科学",
            "成绩": {"数学": 85, "英语": 92, "物理": 78}
        },
        "李四": {
            "年龄": 21,
            "专业": "软件工程",
            "成绩": {"数学": 90, "英语": 88, "物理": 85}
        }
    }
    
    print("学生信息:")
    for name, info in students.items():
        print(f"姓名: {name}")
        print(f"  年龄: {info['年龄']}")
        print(f"  专业: {info['专业']}")
        print(f"  成绩: {info['成绩']}")
        
        # 计算平均分 / Calculate average grade
        grades = info['成绩'].values()
        avg_grade = sum(grades) / len(grades)
        print(f"  平均分: {avg_grade:.2f}")
        print()
    
    # 访问嵌套值 / Accessing nested values
    zhang_math = students["张三"]["成绩"]["数学"]
    print(f"张三的数学成绩: {zhang_math}")
    
    # 修改嵌套值 / Modifying nested values
    students["张三"]["成绩"]["化学"] = 88
    print(f"为张三添加化学成绩后: {students['张三']['成绩']}")

def practical_examples():
    """实际应用示例 / Practical Examples"""
    print("\n=== 实际应用示例 Practical Examples ===")
    
    # 1. 单词计数器 / Word counter
    print("1. 单词计数器示例:")
    text = "python is great python is powerful python is easy"
    word_count = {}
    
    for word in text.split():
        word_count[word] = word_count.get(word, 0) + 1
    
    print(f"单词计数: {word_count}")
    
    # 使用字典推导式的简化版本 / Simplified version using dict comprehension
    from collections import Counter
    word_count_simple = Counter(text.split())
    print(f"使用Counter: {dict(word_count_simple)}")
    
    # 2. 配置管理 / Configuration management
    print("\n2. 配置管理示例:")
    config = {
        "database": {
            "host": "localhost",
            "port": 5432,
            "name": "myapp",
            "user": "admin"
        },
        "cache": {
            "type": "redis",
            "host": "localhost",
            "port": 6379
        },
        "debug": True,
        "version": "1.0.0"
    }
    
    print("应用配置:")
    def print_config(cfg, indent=0):
        for key, value in cfg.items():
            if isinstance(value, dict):
                print("  " * indent + f"{key}:")
                print_config(value, indent + 1)
            else:
                print("  " * indent + f"{key}: {value}")
    
    print_config(config)
    
    # 3. 缓存系统模拟 / Cache system simulation
    print("\n3. 缓存系统示例:")
    cache = {}
    
    def get_user_info(user_id):
        """模拟获取用户信息(带缓存) / Simulate getting user info (with cache)"""
        if user_id in cache:
            print(f"从缓存获取用户 {user_id} 信息")
            return cache[user_id]
        else:
            # 模拟从数据库获取 / Simulate database fetch
            print(f"从数据库获取用户 {user_id} 信息")
            user_info = {"id": user_id, "name": f"用户{user_id}", "email": f"user{user_id}@example.com"}
            cache[user_id] = user_info  # 存入缓存 / Store in cache
            return user_info
    
    # 测试缓存 / Test cache
    user1 = get_user_info(1)  # 从数据库获取
    user1_again = get_user_info(1)  # 从缓存获取
    user2 = get_user_info(2)  # 从数据库获取
    
    print(f"缓存内容: {cache}")

def dict_performance_tips():
    """字典性能提示 / Dictionary Performance Tips"""
    print("\n=== 字典性能提示 Dictionary Performance Tips ===")
    
    import time
    
    # 字典查找 vs 列表查找 / Dictionary lookup vs List lookup
    data_dict = {i: f"value_{i}" for i in range(10000)}
    data_list = [(i, f"value_{i}") for i in range(10000)]
    
    # 字典查找 / Dictionary lookup
    start = time.time()
    for _ in range(1000):
        value = data_dict.get(5000)
    dict_time = time.time() - start
    
    # 列表查找 / List lookup
    start = time.time()
    for _ in range(1000):
        value = None
        for key, val in data_list:
            if key == 5000:
                value = val
                break
    list_time = time.time() - start
    
    print(f"字典查找1000次耗时: {dict_time:.6f}秒")
    print(f"列表查找1000次耗时: {list_time:.6f}秒")
    print(f"字典比列表快 {list_time/dict_time:.1f} 倍")
    
    print("\n性能建议:")
    print("- 需要快速查找时使用字典")
    print("- 字典的键应该是不可变类型(字符串、数字、元组)")
    print("- 避免在循环中频繁创建字典")

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python字典(Dictionary)学习示例")
    print("Python Dictionary Learning Examples")
    print("=" * 50)
    
    basic_dict_operations()
    dict_modification()
    dict_methods()
    dict_comprehension()
    nested_dictionaries()
    practical_examples()
    dict_performance_tips()
    
    print("\n" + "=" * 50)
    print("字典学习完成！Dictionary learning completed!")
