#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
错误与异常处理 (Error and Exception Handling) - Python异常处理机制
Error and Exception Handling - Python Exception Handling Mechanism

异常处理是程序健壮性的重要保障，用于处理程序运行时可能出现的错误情况。
Exception handling is an important guarantee for program robustness, used to handle error conditions that may occur during program execution.
"""

import sys
import traceback
import logging
import os
from typing import Optional, Union

def basic_exception_concepts():
    """基本异常概念 / Basic Exception Concepts"""
    print("=== 基本异常概念 Basic Exception Concepts ===")
    
    print("1. 什么是异常:")
    print("   异常是程序执行过程中发生的错误事件")
    print("   异常会中断程序的正常执行流程")
    print("   Python使用异常对象来表示错误信息")
    
    print("\n2. 常见的内置异常类型:")
    exceptions = {
        "SyntaxError": "语法错误",
        "NameError": "名称错误 (变量未定义)",
        "TypeError": "类型错误",
        "ValueError": "值错误",
        "IndexError": "索引错误",
        "KeyError": "键错误",
        "FileNotFoundError": "文件未找到错误",
        "ZeroDivisionError": "除零错误",
        "AttributeError": "属性错误",
        "ImportError": "导入错误"
    }
    
    for exc_type, description in exceptions.items():
        print(f"   {exc_type}: {description}")
    
    print("\n3. 异常的产生:")
    print("   - 程序逻辑错误")
    print("   - 外部条件不满足 (如文件不存在)")
    print("   - 用户输入不合法")
    print("   - 系统资源不足")

def try_except_basics():
    """try-except基础 / Try-Except Basics"""
    print("\n=== try-except基础 Try-Except Basics ===")
    
    # 1. 基本的try-except结构 / Basic try-except structure
    print("1. 基本异常捕获:")
    
    try:
        result = 10 / 0  # 这会引发ZeroDivisionError
        print(f"结果: {result}")
    except ZeroDivisionError:
        print("错误: 不能除以零!")
    
    print("程序继续执行...")
    
    # 2. 捕获多种异常 / Catching multiple exceptions
    print("\n2. 捕获多种异常:")
    
    def test_multiple_exceptions(value):
        try:
            # 可能引发多种异常的代码
            number = int(value)  # 可能引发ValueError
            result = 100 / number  # 可能引发ZeroDivisionError
            items = [1, 2, 3]
            print(items[number])  # 可能引发IndexError
            
        except ValueError:
            print(f"值错误: '{value}' 不是有效的数字")
        except ZeroDivisionError:
            print("除零错误: 不能除以零")
        except IndexError:
            print(f"索引错误: 索引 {number} 超出范围")
    
    test_values = ["abc", "0", "5", "2"]
    for value in test_values:
        print(f"测试值: {value}")
        test_multiple_exceptions(value)
        print()
    
    # 3. 使用一个except捕获多种异常 / Using one except for multiple exceptions
    print("3. 一个except捕获多种异常:")
    
    try:
        value = input("请输入一个数字 (模拟输入'abc'): ")
        if not value:  # 模拟输入
            value = "abc"
        number = int(value)
        result = 10 / number
        print(f"结果: {result}")
    except (ValueError, ZeroDivisionError) as e:
        print(f"输入或计算错误: {e}")

def exception_information():
    """异常信息获取 / Exception Information Retrieval"""
    print("\n=== 异常信息获取 Exception Information Retrieval ===")
    
    # 1. 获取异常对象 / Get exception object
    print("1. 获取异常对象信息:")
    
    try:
        numbers = [1, 2, 3]
        print(numbers[10])
    except IndexError as e:
        print(f"异常类型: {type(e).__name__}")
        print(f"异常消息: {e}")
        print(f"异常参数: {e.args}")
    
    # 2. 获取详细的异常信息 / Get detailed exception information
    print("\n2. 获取详细异常信息:")
    
    try:
        result = 1 / 0
    except Exception as e:
        print(f"异常类型: {type(e).__name__}")
        print(f"异常消息: {str(e)}")
        
        # 获取异常的详细信息 / Get detailed exception info
        exc_type, exc_value, exc_traceback = sys.exc_info()
        print(f"系统异常类型: {exc_type.__name__}")
        print(f"系统异常值: {exc_value}")
        
        # 打印堆栈跟踪 / Print stack trace
        print("堆栈跟踪:")
        traceback.print_exc()
    
    # 3. 格式化异常信息 / Format exception information
    print("\n3. 格式化异常信息:")
    
    def safe_divide(a, b):
        try:
            return a / b
        except Exception as e:
            error_info = {
                "error_type": type(e).__name__,
                "error_message": str(e),
                "function": "safe_divide",
                "parameters": {"a": a, "b": b}
            }
            return error_info
    
    result = safe_divide(10, 0)
    print(f"安全除法结果: {result}")

def try_except_else_finally():
    """try-except-else-finally完整结构 / Complete try-except-else-finally Structure"""
    print("\n=== try-except-else-finally完整结构 ===")
    
    # 1. else子句 / else clause
    print("1. else子句 - 无异常时执行:")
    
    def process_number(value):
        try:
            number = int(value)
            result = 100 / number
        except ValueError:
            print(f"  错误: '{value}' 不是有效数字")
        except ZeroDivisionError:
            print(f"  错误: 不能除以零")
        else:
            # 只有在没有异常时才执行 / Only executes when no exception occurs
            print(f"  成功: 100 / {number} = {result}")
            return result
    
    test_values = ["5", "0", "abc", "10"]
    for value in test_values:
        print(f"处理值: {value}")
        process_number(value)
    
    # 2. finally子句 / finally clause
    print("\n2. finally子句 - 总是执行:")
    
    def read_file_demo(filename):
        file = None
        try:
            print(f"  尝试打开文件: {filename}")
            file = open(filename, 'r')
            content = file.read()
            print(f"  文件内容长度: {len(content)}")
            return content
        except FileNotFoundError:
            print(f"  错误: 文件 {filename} 不存在")
            return None
        except PermissionError:
            print(f"  错误: 没有权限读取文件 {filename}")
            return None
        else:
            print(f"  成功读取文件 {filename}")
        finally:
            # 无论是否有异常都会执行 / Always executes regardless of exceptions
            if file and not file.closed:
                file.close()
                print(f"  文件 {filename} 已关闭")
            print(f"  清理工作完成")
    
    # 测试存在和不存在的文件 / Test existing and non-existing files
    read_file_demo("nonexistent.txt")
    
    # 创建一个测试文件 / Create a test file
    with open("test_file.txt", "w") as f:
        f.write("这是测试文件内容")
    
    read_file_demo("test_file.txt")
    
    # 清理测试文件 / Clean up test file
    import os
    if os.path.exists("test_file.txt"):
        os.remove("test_file.txt")

def custom_exceptions():
    """自定义异常 / Custom Exceptions"""
    print("\n=== 自定义异常 Custom Exceptions ===")
    
    # 1. 定义自定义异常类 / Define custom exception classes
    class CustomError(Exception):
        """基础自定义异常"""
        pass
    
    class ValidationError(CustomError):
        """数据验证异常"""
        def __init__(self, message, field=None, value=None):
            super().__init__(message)
            self.field = field
            self.value = value
    
    class BusinessLogicError(CustomError):
        """业务逻辑异常"""
        def __init__(self, message, error_code=None):
            super().__init__(message)
            self.error_code = error_code
    
    # 2. 使用自定义异常 / Using custom exceptions
    print("1. 自定义异常示例:")
    
    def validate_age(age):
        """验证年龄"""
        if not isinstance(age, int):
            raise ValidationError("年龄必须是整数", field="age", value=age)
        
        if age < 0:
            raise ValidationError("年龄不能为负数", field="age", value=age)
        
        if age > 150:
            raise ValidationError("年龄不能超过150岁", field="age", value=age)
        
        return True
    
    def process_user_registration(name, age):
        """处理用户注册"""
        try:
            if not name or not name.strip():
                raise ValidationError("姓名不能为空", field="name", value=name)
            
            validate_age(age)
            
            # 模拟业务逻辑检查 / Simulate business logic check
            if name.lower() == "admin":
                raise BusinessLogicError("不能使用保留用户名", error_code="RESERVED_NAME")
            
            print(f"  用户注册成功: {name}, 年龄: {age}")
            return True
            
        except ValidationError as e:
            print(f"  验证错误: {e}")
            if e.field:
                print(f"    字段: {e.field}, 值: {e.value}")
            return False
            
        except BusinessLogicError as e:
            print(f"  业务逻辑错误: {e}")
            if e.error_code:
                print(f"    错误代码: {e.error_code}")
            return False
    
    # 测试自定义异常 / Test custom exceptions
    test_cases = [
        ("张三", 25),
        ("", 30),
        ("李四", -5),
        ("王五", 200),
        ("admin", 30),
        ("赵六", "abc")
    ]
    
    for name, age in test_cases:
        print(f"测试用户: 姓名='{name}', 年龄={age}")
        process_user_registration(name, age)
        print()

def exception_chaining():
    """异常链 / Exception Chaining"""
    print("\n=== 异常链 Exception Chaining ===")
    
    # 1. raise from - 显式异常链 / Explicit exception chaining
    print("1. 显式异常链 (raise from):")
    
    def parse_config_file(filename):
        """解析配置文件"""
        try:
            with open(filename, 'r') as file:
                content = file.read()
                # 模拟JSON解析错误 / Simulate JSON parsing error
                import json
                return json.loads(content)
        except FileNotFoundError as e:
            raise ValueError(f"配置文件 {filename} 无法访问") from e
        except json.JSONDecodeError as e:
            raise ValueError(f"配置文件 {filename} 格式错误") from e
    
    try:
        config = parse_config_file("nonexistent_config.json")
    except ValueError as e:
        print(f"捕获异常: {e}")
        print(f"原始异常: {e.__cause__}")
        print(f"异常类型: {type(e.__cause__).__name__}")
    
    # 2. 异常抑制 / Exception suppression
    print("\n2. 异常抑制 (raise from None):")
    
    def clean_parse_config(filename):
        """清洁的配置解析 - 隐藏底层异常"""
        try:
            with open(filename, 'r') as file:
                import json
                return json.loads(file.read())
        except (FileNotFoundError, json.JSONDecodeError):
            raise ValueError(f"无法解析配置文件: {filename}") from None
    
    try:
        config = clean_parse_config("nonexistent_config.json")
    except ValueError as e:
        print(f"捕获异常: {e}")
        print(f"原始异常: {e.__cause__}")  # 应该是None

def exception_best_practices():
    """异常处理最佳实践 / Exception Handling Best Practices"""
    print("\n=== 异常处理最佳实践 Exception Handling Best Practices ===")
    
    print("异常处理最佳实践:")
    print("1. 具体异常优于通用异常")
    print("2. 不要忽略异常 - 至少要记录日志")
    print("3. 在适当的层级处理异常")
    print("4. 使用finally进行资源清理")
    print("5. 自定义异常要有意义的名称和信息")
    print("6. 不要使用异常来控制正常的程序流程")
    print("7. 记录异常信息用于调试")
    
    # 示例：良好的异常处理实践 / Example: Good exception handling practices
    print("\n良好的异常处理示例:")
    
    # 配置日志 / Configure logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    logger = logging.getLogger(__name__)
    
    def robust_file_processor(filename: str) -> Optional[dict]:
        """
        健壮的文件处理器
        
        Args:
            filename: 要处理的文件名
            
        Returns:
            处理结果字典，失败时返回None
        """
        try:
            # 检查文件是否存在 / Check if file exists
            if not os.path.exists(filename):
                logger.warning(f"文件不存在: {filename}")
                return None
            
            # 尝试读取和处理文件 / Try to read and process file
            with open(filename, 'r', encoding='utf-8') as file:
                content = file.read().strip()
                
                if not content:
                    logger.warning(f"文件为空: {filename}")
                    return {"status": "empty", "filename": filename}
                
                # 模拟处理逻辑 / Simulate processing logic
                word_count = len(content.split())
                char_count = len(content)
                
                result = {
                    "status": "success",
                    "filename": filename,
                    "word_count": word_count,
                    "char_count": char_count
                }
                
                logger.info(f"成功处理文件: {filename}")
                return result
                
        except PermissionError:
            logger.error(f"权限不足，无法读取文件: {filename}")
            return None
        except UnicodeDecodeError:
            logger.error(f"文件编码错误: {filename}")
            return None
        except Exception as e:
            logger.error(f"处理文件时发生未知错误: {filename}, 错误: {e}")
            return None
    
    # 创建测试文件 / Create test file
    with open("test_content.txt", "w", encoding="utf-8") as f:
        f.write("这是一个测试文件，包含一些中文内容。\nThis is a test file with some content.")
    
    # 测试文件处理器 / Test file processor
    result = robust_file_processor("test_content.txt")
    print(f"处理结果: {result}")
    
    result = robust_file_processor("nonexistent.txt")
    print(f"处理结果: {result}")
    
    # 清理测试文件 / Clean up test file
    if os.path.exists("test_content.txt"):
        os.remove("test_content.txt")

def common_exception_patterns():
    """常见异常处理模式 / Common Exception Handling Patterns"""
    print("\n=== 常见异常处理模式 Common Exception Handling Patterns ===")
    
    # 1. 重试模式 / Retry pattern
    print("1. 重试模式:")
    
    def retry_operation(func, max_attempts=3, delay=1):
        """重试操作"""
        import time
        import random
        
        for attempt in range(max_attempts):
            try:
                return func()
            except Exception as e:
                if attempt == max_attempts - 1:
                    print(f"  重试{max_attempts}次后仍然失败: {e}")
                    raise e
                else:
                    print(f"  第{attempt + 1}次尝试失败: {e}, {delay}秒后重试")
                    time.sleep(delay)
    
    def unreliable_operation():
        """不可靠的操作 - 70%概率失败"""
        import random
        if random.random() < 0.7:
            raise ConnectionError("网络连接失败")
        return "操作成功"
    
    try:
        result = retry_operation(unreliable_operation, max_attempts=3, delay=0.1)
        print(f"  最终结果: {result}")
    except Exception as e:
        print(f"  操作最终失败: {e}")
    
    # 2. 默认值模式 / Default value pattern
    print("\n2. 默认值模式:")
    
    def get_config_value(key, default=None):
        """获取配置值，失败时返回默认值"""
        config = {"database_url": "localhost:5432", "debug": True}
        
        try:
            return config[key]
        except KeyError:
            print(f"  配置项 '{key}' 不存在，使用默认值: {default}")
            return default
    
    print(f"  database_url: {get_config_value('database_url')}")
    print(f"  timeout: {get_config_value('timeout', 30)}")
    
    # 3. 资源管理模式 / Resource management pattern
    print("\n3. 资源管理模式:")
    
    class DatabaseConnection:
        """模拟数据库连接"""
        def __init__(self, host):
            self.host = host
            self.connected = False
        
        def connect(self):
            print(f"  连接到数据库: {self.host}")
            self.connected = True
        
        def disconnect(self):
            if self.connected:
                print(f"  断开数据库连接: {self.host}")
                self.connected = False
        
        def execute(self, query):
            if not self.connected:
                raise RuntimeError("数据库未连接")
            print(f"  执行查询: {query}")
            return f"查询结果: {query}"
    
    def database_operation():
        """数据库操作示例"""
        conn = None
        try:
            conn = DatabaseConnection("localhost")
            conn.connect()
            result = conn.execute("SELECT * FROM users")
            return result
        except Exception as e:
            print(f"  数据库操作失败: {e}")
            return None
        finally:
            if conn:
                conn.disconnect()
    
    result = database_operation()
    print(f"  操作结果: {result}")

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python错误与异常处理学习示例")
    print("Python Error and Exception Handling Learning Examples")
    print("=" * 60)
    
    basic_exception_concepts()
    try_except_basics()
    exception_information()
    try_except_else_finally()
    custom_exceptions()
    exception_chaining()
    exception_best_practices()
    common_exception_patterns()
    
    print("\n" + "=" * 60)
    print("错误与异常处理学习完成！Error and exception handling learning completed!")
