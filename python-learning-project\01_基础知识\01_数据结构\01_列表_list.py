#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
列表 (List) - Python基础数据结构
Lists - Python Basic Data Structure

列表是Python中最常用的数据结构之一，它是有序、可变的集合。
Lists are one of the most commonly used data structures in Python, they are ordered and mutable collections.
"""

def basic_list_operations():
    """基本列表操作 / Basic List Operations"""
    print("=== 基本列表操作 Basic List Operations ===")
    
    # 创建列表 / Creating lists
    empty_list = []  # 空列表 / Empty list
    numbers = [1, 2, 3, 4, 5]  # 数字列表 / Number list
    fruits = ["苹果", "香蕉", "橙子"]  # 字符串列表 / String list
    mixed = [1, "hello", 3.14, True]  # 混合类型列表 / Mixed type list
    
    print(f"空列表: {empty_list}")
    print(f"数字列表: {numbers}")
    print(f"水果列表: {fruits}")
    print(f"混合列表: {mixed}")
    
    # 访问元素 / Accessing elements
    print(f"\n第一个水果: {fruits[0]}")  # 索引从0开始 / Index starts from 0
    print(f"最后一个水果: {fruits[-1]}")  # 负索引 / Negative index
    
    # 列表长度 / List length
    print(f"水果列表长度: {len(fruits)}")
    
    return fruits

def list_modification():
    """列表修改操作 / List Modification Operations"""
    print("\n=== 列表修改操作 List Modification Operations ===")
    
    fruits = ["苹果", "香蕉", "橙子"]
    print(f"原始列表: {fruits}")
    
    # 添加元素 / Adding elements
    fruits.append("葡萄")  # 在末尾添加 / Add at the end
    print(f"添加葡萄后: {fruits}")
    
    fruits.insert(1, "草莓")  # 在指定位置插入 / Insert at specific position
    print(f"在位置1插入草莓: {fruits}")
    
    # 修改元素 / Modifying elements
    fruits[0] = "红苹果"  # 修改第一个元素 / Modify first element
    print(f"修改第一个元素: {fruits}")
    
    # 删除元素 / Removing elements
    removed = fruits.pop()  # 删除并返回最后一个元素 / Remove and return last element
    print(f"删除的元素: {removed}, 剩余: {fruits}")
    
    fruits.remove("香蕉")  # 删除指定值的第一个匹配项 / Remove first occurrence of value
    print(f"删除香蕉后: {fruits}")
    
    del fruits[1]  # 删除指定索引的元素 / Delete element at specific index
    print(f"删除索引1的元素后: {fruits}")
    
    return fruits

def list_slicing():
    """列表切片操作 / List Slicing Operations"""
    print("\n=== 列表切片操作 List Slicing Operations ===")
    
    numbers = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
    print(f"原始列表: {numbers}")
    
    # 基本切片 / Basic slicing
    print(f"前5个元素: {numbers[:5]}")  # [start:end]
    print(f"从索引3开始: {numbers[3:]}")
    print(f"中间部分 [2:7]: {numbers[2:7]}")
    
    # 步长切片 / Step slicing
    print(f"每隔一个元素: {numbers[::2]}")  # [start:end:step]
    print(f"倒序: {numbers[::-1]}")
    print(f"倒序每隔一个: {numbers[::-2]}")
    
    # 负索引切片 / Negative index slicing
    print(f"最后3个元素: {numbers[-3:]}")
    print(f"除了最后2个: {numbers[:-2]}")

def list_methods():
    """列表常用方法 / Common List Methods"""
    print("\n=== 列表常用方法 Common List Methods ===")
    
    numbers = [3, 1, 4, 1, 5, 9, 2, 6]
    print(f"原始列表: {numbers}")
    
    # 排序 / Sorting
    sorted_numbers = sorted(numbers)  # 返回新的排序列表 / Return new sorted list
    print(f"排序后(新列表): {sorted_numbers}")
    print(f"原列表未变: {numbers}")
    
    numbers.sort()  # 就地排序 / Sort in place
    print(f"就地排序后: {numbers}")
    
    numbers.reverse()  # 反转列表 / Reverse list
    print(f"反转后: {numbers}")
    
    # 查找和计数 / Finding and counting
    print(f"数字1的索引: {numbers.index(1)}")
    print(f"数字1出现次数: {numbers.count(1)}")
    
    # 扩展列表 / Extending list
    more_numbers = [7, 8]
    numbers.extend(more_numbers)  # 添加另一个列表的所有元素 / Add all elements from another list
    print(f"扩展后: {numbers}")

def list_comprehension():
    """列表推导式 / List Comprehension"""
    print("\n=== 列表推导式 List Comprehension ===")
    
    # 基本列表推导式 / Basic list comprehension
    squares = [x**2 for x in range(10)]
    print(f"0-9的平方: {squares}")
    
    # 带条件的列表推导式 / List comprehension with condition
    even_squares = [x**2 for x in range(10) if x % 2 == 0]
    print(f"偶数的平方: {even_squares}")
    
    # 字符串处理 / String processing
    words = ["hello", "world", "python", "programming"]
    upper_words = [word.upper() for word in words]
    print(f"大写单词: {upper_words}")
    
    # 嵌套列表推导式 / Nested list comprehension
    matrix = [[i*j for j in range(1, 4)] for i in range(1, 4)]
    print(f"乘法表矩阵: {matrix}")

def practical_examples():
    """实际应用示例 / Practical Examples"""
    print("\n=== 实际应用示例 Practical Examples ===")
    
    # 学生成绩管理 / Student grade management
    students = ["张三", "李四", "王五", "赵六"]
    grades = [85, 92, 78, 96]
    
    # 创建学生-成绩对应关系 / Create student-grade mapping
    student_grades = list(zip(students, grades))
    print(f"学生成绩对应: {student_grades}")
    
    # 找出优秀学生(成绩>=90) / Find excellent students (grade >= 90)
    excellent_students = [name for name, grade in student_grades if grade >= 90]
    print(f"优秀学生: {excellent_students}")
    
    # 计算平均分 / Calculate average grade
    average_grade = sum(grades) / len(grades)
    print(f"平均分: {average_grade:.2f}")
    
    # 购物清单管理 / Shopping list management
    shopping_list = []
    items_to_buy = ["牛奶", "面包", "鸡蛋", "苹果"]
    
    for item in items_to_buy:
        shopping_list.append(item)
        print(f"添加 {item} 到购物清单")
    
    print(f"完整购物清单: {shopping_list}")
    
    # 模拟购买过程 / Simulate buying process
    bought_items = []
    while shopping_list:
        item = shopping_list.pop(0)  # 取出第一个物品 / Take first item
        bought_items.append(item)
        print(f"购买了 {item}, 剩余: {shopping_list}")
    
    print(f"已购买物品: {bought_items}")

if __name__ == "__main__":
    """主函数 - 运行所有示例 / Main function - Run all examples"""
    print("Python列表(List)学习示例")
    print("Python List Learning Examples")
    print("=" * 50)
    
    basic_list_operations()
    list_modification()
    list_slicing()
    list_methods()
    list_comprehension()
    practical_examples()
    
    print("\n" + "=" * 50)
    print("列表学习完成！List learning completed!")
