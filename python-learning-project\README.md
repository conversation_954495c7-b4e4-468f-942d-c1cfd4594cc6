# Python学习项目 / Python Learning Project

这是一个全面的Python基础和进阶知识学习项目，包含详细的代码示例、实践练习和学习指导。

This is a comprehensive Python fundamentals and advanced concepts learning project, containing detailed code examples, practical exercises, and learning guidance.

## 🎯 项目目标 / Project Goals

- 提供结构化的Python学习路径
- 包含丰富的实际代码示例
- 支持中英文双语学习
- 涵盖基础到进阶的完整知识体系

## 📚 学习内容 / Learning Content

### 01_基础知识 / Fundamentals
- ✅ **数据结构**: 列表、元组、字典、集合
- ✅ **条件语句**: if、elif、else语句
- ✅ **循环语句**: for循环、while循环
- ✅ **文件输入输出**: 文件读写操作
- ✅ **自定义函数**: 函数定义和调用
- ✅ **错误与异常处理**: try、except、finally
- ✅ **面向对象编程**: 类、对象、继承
- ✅ **模块**: 模块导入和使用

### 02_进阶知识 / Advanced Topics
- ✅ **对象和深浅复制**: copy、deepcopy
- ✅ **参数传递**: 位置参数、关键字参数
- ✅ **迭代器**: iterator协议
- ✅ **生成器**: generator函数
- ✅ **装饰器**: decorator模式
- ✅ **元类**: metaclass概念
- ✅ **运算符重载**: 魔术方法
- ✅ **上下文管理器**: with语句
- ✅ **并发编程**: 多线程、多进程
- ✅ **全局解释器锁**: GIL机制
- ✅ **垃圾回收机制**: 内存管理
- ✅ **Python与其他语言结合**: C扩展

## 🚀 快速开始 / Quick Start

### 环境要求 / Requirements
- Python 3.8+
- uv (Python包管理工具)

### 安装依赖 / Install Dependencies
```bash
# 使用uv安装依赖
uv sync

# 或使用pip
pip install -r requirements.txt
```

### 运行示例 / Run Examples
```bash
# 运行特定模块
python 01_基础知识/01_数据结构/01_列表_list.py

# 运行所有基础知识示例
cd 01_基础知识
for dir in */; do
    echo "=== $dir ==="
    cd "$dir"
    for file in *.py; do
        echo "Running $file"
        python "$file"
    done
    cd ..
done
```

## 📖 学习建议 / Learning Tips

### 1. 学习顺序 / Learning Order
1. 先完成基础知识部分
2. 每个模块都要动手实践
3. 完成相关练习题
4. 再进入进阶知识学习

### 2. 实践方法 / Practice Methods
- 阅读代码示例
- 运行并观察输出
- 修改代码参数试验
- 完成练习题
- 创建自己的项目

### 3. 学习资源 / Learning Resources
- 每个模块都有详细的README
- 代码包含中英文注释
- 提供实际应用示例
- 包含性能对比和最佳实践

## 🎓 项目结构 / Project Structure

```
python-learning-project/
├── 01_基础知识/                    # 基础知识模块
│   ├── 01_数据结构/                # 数据结构
│   ├── 02_条件语句/                # 条件语句
│   ├── 03_循环语句/                # 循环语句
│   ├── 04_文件输入输出/            # 文件操作
│   ├── 05_自定义函数/              # 函数
│   ├── 06_错误与异常处理/          # 异常处理
│   ├── 07_面向对象编程/            # OOP
│   └── 08_模块/                    # 模块
├── 02_进阶知识/                    # 进阶知识模块
│   ├── 01_对象和深浅复制/          # 对象复制
│   ├── 02_参数传递/                # 参数传递
│   ├── 03_迭代器/                  # 迭代器
│   ├── 04_生成器/                  # 生成器
│   ├── 05_装饰器/                  # 装饰器
│   ├── 06_元类/                    # 元类
│   ├── 07_运算符重载/              # 运算符重载
│   ├── 08_上下文管理器/            # 上下文管理器
│   ├── 09_并发编程/                # 并发编程
│   ├── 10_全局解释器锁/            # GIL
│   ├── 11_垃圾回收机制/            # 垃圾回收
│   └── 12_Python用其他语言结合/    # 语言结合
├── 练习题/                         # 练习题目
├── 项目实战/                       # 实战项目
├── pyproject.toml                  # 项目配置
└── README.md                       # 项目说明
```

## 🔧 开发工具 / Development Tools

本项目使用以下工具：
- **uv**: Python包管理和虚拟环境
- **pytest**: 单元测试框架
- **black**: 代码格式化
- **flake8**: 代码质量检查

## 📊 学习进度跟踪 / Progress Tracking

建议创建学习日志，记录：
- [ ] 完成的模块
- [ ] 理解的概念
- [ ] 遇到的问题
- [ ] 实践的项目

## 🤝 贡献指南 / Contributing

欢迎提交问题和改进建议：
1. Fork本项目
2. 创建特性分支
3. 提交更改
4. 发起Pull Request

## 📄 许可证 / License

本项目采用MIT许可证 - 查看[LICENSE](LICENSE)文件了解详情。

## 🙏 致谢 / Acknowledgments

感谢所有为Python社区做出贡献的开发者们！

---

**开始学习**: [基础知识 - 数据结构](01_基础知识/01_数据结构/README.md)

**Start Learning**: [Fundamentals - Data Structures](01_基础知识/01_数据结构/README.md)